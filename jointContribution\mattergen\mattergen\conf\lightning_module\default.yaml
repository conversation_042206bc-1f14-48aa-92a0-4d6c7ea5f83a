
_target_: mattergen.diffusion.lightning_module.DiffusionLightningModule
optimizer_partial:
  _target_: mattergen.optimizer.build.build_optimizer
  cfg:
    __name__: Adam
    beta1: 0.9
    beta2: 0.999
    clip_value: 0.5
    lr:
      __name__: ReduceOnPlateau
      learning_rate: 0.0001
      factor: 0.6
      by_epoch: True
      patience: 100
      min_lr: 0.000001
      indicator: "train_loss"

