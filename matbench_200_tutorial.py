#!/usr/bin/env python3
"""
MatBench 200样本数据集使用教程
"""
import os
import subprocess
import time
from pathlib import Path

def check_prerequisites():
    """检查前置条件"""
    print("🔍 检查前置条件...")
    
    # 检查数据文件
    data_file = "./data/matbench/matbench_mp_e_form_test_200.pkl"
    if not os.path.exists(data_file):
        print(f"❌ 数据文件不存在: {data_file}")
        return False
    print(f"✅ 数据文件存在: {data_file}")
    
    # 检查配置文件
    config_file = "./property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml"
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    print(f"✅ 配置文件存在: {config_file}")
    
    # 检查训练脚本
    train_script = "./property_prediction/train.py"
    if not os.path.exists(train_script):
        print(f"❌ 训练脚本不存在: {train_script}")
        return False
    print(f"✅ 训练脚本存在: {train_script}")
    
    return True

def run_quick_training():
    """运行快速训练"""
    print("\n🚀 开始200样本快速训练...")
    
    cmd = "python property_prediction/train.py -c property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml"
    
    print(f"执行命令: {cmd}")
    print("预计训练时间: 20-30分钟")
    print("=" * 60)
    
    start_time = time.time()
    
    try:
        # 实时显示输出
        process = subprocess.Popen(
            cmd,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # 实时打印输出
        for line in process.stdout:
            print(line.rstrip())
        
        process.wait()
        
        end_time = time.time()
        duration = end_time - start_time
        
        if process.returncode == 0:
            print(f"\n✅ 训练完成! 用时: {duration/60:.1f} 分钟")
            return True
        else:
            print(f"\n❌ 训练失败! 返回码: {process.returncode}")
            return False
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断训练")
        return False
    except Exception as e:
        print(f"\n❌ 训练异常: {e}")
        return False

def analyze_results():
    """分析训练结果"""
    print("\n📊 分析训练结果...")
    
    # 查找输出目录
    output_pattern = "./output/comformer_matbench_mp_e_form_test_*"
    import glob
    output_dirs = glob.glob(output_pattern)
    
    if not output_dirs:
        print("❌ 未找到训练输出目录")
        return False
    
    # 使用最新的输出目录
    latest_output = max(output_dirs, key=os.path.getctime)
    print(f"📁 输出目录: {latest_output}")
    
    # 检查日志文件
    log_file = os.path.join(latest_output, "run.log")
    if os.path.exists(log_file):
        print(f"📄 日志文件: {log_file}")
        
        # 提取关键信息
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 查找最终结果
            final_metrics = []
            for line in reversed(lines[-50:]):  # 检查最后50行
                if "Eval:" in line and "e_form(metric)" in line:
                    final_metrics.append(line.strip())
                    if len(final_metrics) >= 3:  # 获取最后3个评估结果
                        break
            
            if final_metrics:
                print("\n📈 最终评估结果:")
                for metric in reversed(final_metrics):
                    print(f"  {metric}")
            
        except Exception as e:
            print(f"⚠️ 读取日志文件失败: {e}")
    
    # 检查模型文件
    checkpoints_dir = os.path.join(latest_output, "checkpoints")
    if os.path.exists(checkpoints_dir):
        print(f"💾 模型检查点: {checkpoints_dir}")
        
        # 列出可用的检查点
        checkpoints = []
        for item in os.listdir(checkpoints_dir):
            checkpoint_path = os.path.join(checkpoints_dir, item)
            if os.path.isdir(checkpoint_path):
                checkpoints.append(item)
        
        if checkpoints:
            print("  可用检查点:")
            for cp in sorted(checkpoints):
                print(f"    - {cp}")
    
    return True

def run_prediction_test():
    """运行预测测试"""
    print("\n🔮 运行预测测试...")
    
    # 查找最新的模型
    output_pattern = "./output/comformer_matbench_mp_e_form_test_*"
    import glob
    output_dirs = glob.glob(output_pattern)
    
    if not output_dirs:
        print("❌ 未找到训练好的模型")
        return False
    
    latest_output = max(output_dirs, key=os.path.getctime)
    best_model_path = os.path.join(latest_output, "checkpoints", "best")
    
    if not os.path.exists(best_model_path):
        print(f"❌ 最佳模型不存在: {best_model_path}")
        return False
    
    # 检查示例CIF文件
    cif_path = "./property_prediction/example_data/cifs/"
    if not os.path.exists(cif_path):
        print(f"❌ 示例CIF文件目录不存在: {cif_path}")
        return False
    
    # 构建预测命令
    config_path = "./property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml"
    cmd = f"python property_prediction/predict.py --config_path='{config_path}' --weights_path='{best_model_path}' --cif_file_path='{cif_path}'"
    
    print(f"执行预测命令: {cmd}")
    
    try:
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=True,
            text=True,
            timeout=120
        )
        
        if result.returncode == 0:
            print("✅ 预测成功!")
            print("预测输出:")
            print(result.stdout)
            
            # 检查结果文件
            if os.path.exists("result.csv"):
                print("📄 预测结果已保存到 result.csv")
                
                # 显示结果
                try:
                    import pandas as pd
                    df = pd.read_csv("result.csv")
                    print("预测结果:")
                    print(df.to_string(index=False))
                except:
                    print("⚠️ 无法读取CSV结果文件")
            
            return True
        else:
            print("❌ 预测失败!")
            print("错误信息:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 预测超时")
        return False
    except Exception as e:
        print(f"❌ 预测异常: {e}")
        return False

def show_tutorial_menu():
    """显示教程菜单"""
    print("\n" + "="*60)
    print("🎓 MatBench 200样本数据集使用教程")
    print("="*60)
    print("请选择操作:")
    print("1. 检查前置条件")
    print("2. 开始快速训练 (20-30分钟)")
    print("3. 分析训练结果")
    print("4. 运行预测测试")
    print("5. 完整流程演示")
    print("6. 查看配置文件说明")
    print("0. 退出")
    print("="*60)

def show_config_explanation():
    """显示配置文件说明"""
    print("\n📋 配置文件说明")
    print("="*60)
    
    config_explanations = {
        "数据集配置": {
            "task_name": "matbench_mp_e_form_test_200 - 使用200样本数据",
            "max_samples": "200 - 限制最大样本数量",
            "batch_size": "32 - 每批次处理32个样本"
        },
        "训练配置": {
            "max_epochs": "50 - 最大训练轮数",
            "save_freq": "20 - 每20轮保存一次模型",
            "eval_freq": "10 - 每10轮评估一次"
        },
        "模型配置": {
            "conv_layers": "4 - Comformer卷积层数",
            "node_features": "256 - 节点特征维度",
            "edge_features": "256 - 边特征维度"
        },
        "优化器配置": {
            "max_learning_rate": "0.001 - 最大学习率",
            "scheduler": "OneCycleLR - 学习率调度器"
        }
    }
    
    for category, configs in config_explanations.items():
        print(f"\n🔧 {category}:")
        for key, desc in configs.items():
            print(f"  • {key}: {desc}")
    
    print(f"\n📁 配置文件位置:")
    print(f"  property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml")

def run_complete_demo():
    """运行完整流程演示"""
    print("\n🎬 完整流程演示")
    print("="*60)
    
    steps = [
        ("检查前置条件", check_prerequisites),
        ("开始训练", run_quick_training),
        ("分析结果", analyze_results),
        ("预测测试", run_prediction_test)
    ]
    
    for i, (step_name, step_func) in enumerate(steps, 1):
        print(f"\n步骤 {i}: {step_name}")
        print("-" * 40)
        
        if not step_func():
            print(f"❌ 步骤 {i} 失败，停止演示")
            return False
        
        if i < len(steps):
            input("\n按回车键继续下一步...")
    
    print("\n🎉 完整流程演示完成!")
    print("您已经成功:")
    print("✅ 训练了一个Comformer模型")
    print("✅ 分析了训练结果")
    print("✅ 运行了预测测试")
    print("\n下一步建议:")
    print("• 尝试使用500样本或完整数据集训练")
    print("• 调整模型参数进行实验")
    print("• 在自己的CIF文件上测试预测")
    
    return True

def main():
    """主函数"""
    while True:
        show_tutorial_menu()
        
        try:
            choice = input("请输入选择 (0-6): ").strip()
            
            if choice == '0':
                print("👋 退出教程")
                break
            elif choice == '1':
                check_prerequisites()
            elif choice == '2':
                run_quick_training()
            elif choice == '3':
                analyze_results()
            elif choice == '4':
                run_prediction_test()
            elif choice == '5':
                run_complete_demo()
            elif choice == '6':
                show_config_explanation()
            else:
                print("❌ 无效选择，请输入0-6之间的数字")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出教程")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
        
        if choice != '5':  # 完整演示不需要额外暂停
            input("\n按回车键继续...")

if __name__ == "__main__":
    main()
