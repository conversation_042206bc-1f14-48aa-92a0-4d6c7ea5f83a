#!/usr/bin/env python3
"""
下载完整的MatBench数据集
"""
import os
import pickle
import sys
from pathlib import Path

def check_dependencies():
    """检查必要的依赖包"""
    try:
        import matbench
        from matbench.bench import MatbenchBenchmark
        import pymatgen
        print("✓ 所有依赖包已安装")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖包: {e}")
        print("请运行以下命令安装:")
        print("pip install matbench pymatgen")
        return False

def download_matbench_data():
    """下载完整的MatBench数据集"""

    # 检查依赖
    if not check_dependencies():
        return False

    from matbench.bench import MatbenchBenchmark

    # 创建数据目录
    data_dir = Path("./data/matbench")
    data_dir.mkdir(parents=True, exist_ok=True)

    print("开始下载MatBench e_form数据集...")
    print("注意: 首次下载可能需要较长时间，请耐心等待")

    try:
        # 初始化MatBench基准测试
        mb = MatbenchBenchmark(autoload=False)

        # 下载matbench_mp_e_form任务数据
        task_name = "matbench_mp_e_form"
        print(f"正在下载 {task_name} 数据集...")

        # 加载任务数据
        task = mb.matbench_mp_e_form
        task.load()

        # 获取完整数据
        df = task.df
        print(f"完整数据集大小: {len(df)} 个样本")

        # 转换为我们需要的格式
        print("正在转换数据格式...")
        graphs_dict = {}
        props_dict = {"e_form": []}

        for i, (idx, row) in enumerate(df.iterrows()):
            # 存储结构信息（使用pymatgen Structure对象）
            graphs_dict[i] = row['structure']
            # 存储属性值
            props_dict["e_form"].append(row['e_form'])

            if (i + 1) % 5000 == 0:
                print(f"已处理 {i + 1}/{len(df)} 个样本 ({(i+1)/len(df)*100:.1f}%)")

        # 保存完整数据集
        full_data = {
            'graphs': graphs_dict,
            'props': props_dict
        }

        output_file_full = data_dir / "matbench_mp_e_form_full.pkl"
        print(f"正在保存完整数据集到: {output_file_full}")

        with open(output_file_full, 'wb') as f:
            pickle.dump(full_data, f)

        print("✓ 完整数据集保存完成!")

        # 创建1000样本子集
        print("\n正在创建1000样本子集...")
        subset_size = 1000

        if len(graphs_dict) >= subset_size:
            # 创建1000样本子集
            subset_graphs = {i: graphs_dict[i] for i in range(subset_size)}
            subset_props = {"e_form": props_dict["e_form"][:subset_size]}

            subset_data = {
                'graphs': subset_graphs,
                'props': subset_props
            }

            output_file_1k = data_dir / "matbench_mp_e_form_1000.pkl"
            with open(output_file_1k, 'wb') as f:
                pickle.dump(subset_data, f)

            print(f"✓ 1000样本子集已保存到: {output_file_1k}")

        # 显示数据统计信息
        e_form_values = props_dict["e_form"]
        print(f"\n数据统计:")
        print(f"  完整数据集: {len(graphs_dict)} 个样本")
        print(f"  1000样本子集: {min(len(graphs_dict), subset_size)} 个样本")
        print(f"  形成能范围: {min(e_form_values):.3f} ~ {max(e_form_values):.3f} eV/atom")
        print(f"  平均形成能: {sum(e_form_values)/len(e_form_values):.3f} eV/atom")

        return True

    except Exception as e:
        print(f"✗ 下载失败: {e}")
        print("请检查网络连接或稍后重试")
        return False

def create_full_config():
    """创建完整数据集的配置文件"""
    
    config_content = """Global:
  label_names:
  - e_form
  do_train: true
  do_eval: true
  do_test: true
  split_dataset_ratio:
    train: 0.8
    val: 0.1
    test: 0.1
  graph_converter:
    __class_name__: ComformerGraphConverter
    __init_params__:
      cutoff: 5.0
      num_cpus: 8
      atom_features: cgcnn
      max_neighbors: 25

Trainer:
  max_epochs: 200
  seed: 42
  output_dir: ./output/comformer_matbench_mp_e_form_full
  save_freq: 50
  log_freq: 100
  start_eval_epoch: 10
  eval_freq: 20
  pretrained_model_path: null
  resume_from_checkpoint: null
  use_amp: true
  amp_level: O1
  eval_with_no_grad: true
  gradient_accumulation_steps: 2
  best_metric_indicator: eval_metric
  name_for_best_metric: e_form
  greater_is_better: false
  compute_metric_during_train: true
  metric_strategy_during_eval: epoch
  use_visualdl: false
  use_wandb: false
  use_tensorboard: false

Model:
  __class_name__: iComformer
  __init_params__:
    conv_layers: 4
    edge_layers: 1
    atom_input_features: 92
    edge_features: 256
    triplet_input_features: 256
    node_features: 256
    fc_features: 256
    output_features: 1
    node_layer_head: 1
    edge_layer_head: 1
    property_name: ${Global.label_names}

Metric:
  e_form:
    __class_name__: paddle.nn.L1Loss
    __init_params__: {}

Optimizer:
  __class_name__: AdamW
  __init_params__:
    lr:
      __class_name__: OneCycleLR
      __init_params__:
        max_learning_rate: 0.001

Dataset:
  train:
    dataset:
      __class_name__: MatBenchDataset
      __init_params__:
        path: ./data/matbench
        task_name: matbench_mp_e_form_full
        property_name: e_form
        overwrite: false
        transforms:
        - __class_name__: ComformerCompatTransform
          __init_params__: {}
    num_workers: 4
    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: true
        drop_last: true
        batch_size: 64
  val:
    dataset:
      __class_name__: MatBenchDataset
      __init_params__:
        path: ./data/matbench
        task_name: matbench_mp_e_form_full
        property_name: e_form
        overwrite: false
        transforms:
        - __class_name__: ComformerCompatTransform
          __init_params__: {}
    num_workers: 4
    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: false
        drop_last: false
        batch_size: 64
  test:
    dataset:
      __class_name__: MatBenchDataset
      __init_params__:
        path: ./data/matbench
        task_name: matbench_mp_e_form_full
        property_name: e_form
        overwrite: false
        transforms:
        - __class_name__: ComformerCompatTransform
          __init_params__: {}
    num_workers: 4
    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: false
        drop_last: false
        batch_size: 64

Predict:
  graph_converter: ${Global.graph_converter}
  eval_with_no_grad: true
"""
    
    config_dir = Path("property_prediction/configs/comformer")
    config_dir.mkdir(parents=True, exist_ok=True)
    
    config_file = config_dir / "comformer_matbench_mp_e_form_full.yaml"
    
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"✓ 配置文件已创建: {config_file}")

def main():
    """主函数"""
    print("MatBench完整数据集下载工具")
    print("=" * 50)
    
    # 下载数据
    if download_matbench_data():
        # 创建配置文件
        create_full_config()
        
        print("\n" + "=" * 50)
        print("✓ 所有任务完成!")
        print("\n下一步操作:")
        print("1. 安装训练环境依赖")
        print("2. 运行训练命令:")
        print("   python property_prediction/train.py -c property_prediction/configs/comformer/comformer_matbench_mp_e_form_full.yaml")
        print("\n注意: 完整数据集训练需要较长时间和大量计算资源")
    else:
        print("✗ 数据下载失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
