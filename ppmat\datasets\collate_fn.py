# Copyright (c) 2024 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import numpy as np
import paddle
from collections.abc import Mapping, Sequence

try:
    import pgl
except ImportError:
    pgl = None

from ppmat.datasets.concat_data import ConcatData


class DefaultCollator:
    """
    Default collate function for batching data.
    Fixed version to handle inconsistent sample fields.
    """

    def __call__(self, batch):
        sample = batch[0]
        if isinstance(sample, paddle.Tensor):
            return paddle.stack(batch, axis=0)
        elif isinstance(sample, (int, float)):
            return paddle.to_tensor(batch)
        elif isinstance(sample, np.ndarray):
            try:
                batch = paddle.to_tensor(batch)
            except:
                batch = np.array(batch)
            return batch
        elif isinstance(sample, (str, bytes)):
            return batch
        elif isinstance(sample, Mapping):
            # 修复：检查所有样本是否有相同的键
            all_keys = [set(d.keys()) for d in batch]
            
            # 如果键不一致，打印调试信息并使用共同键
            if not all(keys == all_keys[0] for keys in all_keys):
                print(f"Warning: Inconsistent keys in batch samples:")
                for i, keys in enumerate(all_keys):
                    print(f"  Sample {i}: {sorted(keys)}")
                
                # 使用所有样本共有的键
                common_keys = set.intersection(*all_keys)
                print(f"  Using common keys: {sorted(common_keys)}")
                
                if not common_keys:
                    raise RuntimeError("No common keys found among samples in batch")
                
                # 只处理共同的键
                result = {}
                for key in common_keys:
                    try:
                        result[key] = self([d[key] for d in batch if key in d])
                    except Exception as e:
                        print(f"  Error processing key '{key}': {e}")
                        continue
                return result
            
            # 正常情况：所有样本有相同的键
            return {key: self([d[key] for d in batch]) for key in sample}
            
        elif isinstance(sample, Sequence):
            sample_fields_num = len(sample)
            if not all(len(sample) == sample_fields_num for sample in iter(batch)):
                # 修复：提供更详细的错误信息
                field_lengths = [len(s) for s in batch]
                print(f"Error: Inconsistent field numbers in batch:")
                print(f"  Expected: {sample_fields_num}")
                print(f"  Got: {field_lengths}")
                raise RuntimeError("Fields number not same among samples in a batch")
            return [self(fields) for fields in zip(*batch)]
        elif str(type(sample)) == "<class 'pgl.graph.Graph'>":
            # use str(type()) instead of isinstance() in case of pgl is not installed.
            graphs = pgl.Graph.batch(batch)
            # NOTE: when num_works >1, graphs.tensor() will convert numpy.ndarray to
            # CPU Tensor, which will cause error in model training.
            # graphs.tensor()
            return graphs
        elif isinstance(sample, ConcatData):
            return ConcatData.batch(batch)
        raise TypeError(
            "batch data can only contains: paddle.Tensor, numpy.ndarray, "
            f"dict, list, number, None, pgl.Graph, but got {type(sample)}"
        )
