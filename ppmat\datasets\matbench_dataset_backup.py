# Copyright (c) 2023 PaddlePaddle Authors. All Rights Reserved.

# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at

#     http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from __future__ import absolute_import
from __future__ import annotations

import math
import os
import os.path as osp
import pickle
from collections import defaultdict
from typing import Any
from typing import Callable
from typing import Dict
from typing import Optional

import numpy as np
import paddle.distributed as dist
from paddle.io import Dataset

from ppmat.datasets.build_structure import BuildStructure
from ppmat.datasets.custom_data_type import ConcatData
from ppmat.models import build_graph_converter
from ppmat.utils import logger
from ppmat.utils.misc import is_equal


class MatBenchDataset(Dataset):
    """MatBench Dataset Handler
    
    MatBench是材料科学机器学习的标准基准数据集，包含多个任务。
    本实现支持matbench_mp_e_form任务，用于预测材料的形成能。
    
    Args:
        path (str): 数据集路径，应该指向包含matbench数据的目录
        task_name (str): MatBench任务名称，如'matbench_mp_e_form'
        property_name (str): 属性名称，如'e_form'
        build_structure_cfg (Dict, optional): 构建pymatgen结构的配置
        build_graph_cfg (Dict, optional): 构建图的配置
        transforms (Optional[Callable], optional): 数据预处理变换
        cache_path (Optional[str], optional): 缓存路径
        overwrite (bool, optional): 是否覆盖现有缓存
        filter_unvalid (bool, optional): 是否过滤无效样本
        max_samples (Optional[int], optional): 最大样本数量，用于测试
    """
    
    def __init__(
        self,
        path: str,
        task_name: str = "matbench_mp_e_form_full",
        property_name: str = "e_form",
        build_structure_cfg: Dict = None,
        build_graph_cfg: Dict = None,
        transforms: Optional[Callable] = None,
        cache_path: Optional[str] = None,
        overwrite: bool = False,
        filter_unvalid: bool = True,
        max_samples: Optional[int] = None,
        **kwargs,  # for compatibility
    ):
        super().__init__()

        # 构建数据文件路径
        data_file = osp.join(path, f"{task_name}.pkl")
        if not osp.exists(data_file):
            raise FileNotFoundError(f"MatBench数据文件不存在: {data_file}")

        self.path = data_file
        self.task_name = task_name
        self.max_samples = max_samples

        if isinstance(property_name, str):
            property_name = [property_name]

        if build_structure_cfg is None:
            build_structure_cfg = {
                "format": "pymatgen_structure",
                "primitive": False,
                "niggli": True,
                "num_cpus": 1,
            }
            logger.message(
                "build_structure_cfg未设置，使用默认配置: "
                f"{build_structure_cfg}"
            )

        self.property_names = property_name if property_name is not None else []
        self.build_structure_cfg = build_structure_cfg
        self.build_graph_cfg = build_graph_cfg
        self.transforms = transforms

        if cache_path is not None:
            self.cache_path = cache_path
        else:
            # 类似MP2018Dataset的缓存路径生成
            cache_suffix = f"_{max_samples}" if max_samples else ""
            self.cache_path = osp.join(
                osp.split(data_file)[0] + "_cache",
                osp.splitext(osp.basename(data_file))[0] + cache_suffix
            )
        logger.info(f"缓存路径: {self.cache_path}")

        self.overwrite = overwrite
        self.filter_unvalid = filter_unvalid

        # 检查缓存是否存在
        self.cache_exists = True if osp.exists(self.cache_path) else False

        # 读取原始数据 - 类似MP2018Dataset的流程
        self.row_data, self.num_samples = self.read_data(data_file)
        logger.info(f"从 {data_file} 加载 {self.num_samples} 个样本")

        # 读取属性数据
        self.property_data = self.read_property_data(self.row_data, self.property_names)
        
        self.build_structure_cfg = build_structure_cfg
        self.build_graph_cfg = build_graph_cfg
        self.transforms = transforms
        
        # 设置缓存路径
        if cache_path is not None:
            self.cache_path = cache_path
        else:
            cache_suffix = f"_{max_samples}" if max_samples else ""
            if build_graph_cfg:
                graph_name = build_graph_cfg['__class_name__'].lower()
                cutoff = str(int(build_graph_cfg['__init_params__']['cutoff']))
                self.cache_path = osp.join(
                    osp.dirname(path), 
                    f"{task_name}_cache_{graph_name}_cutoff_{cutoff}{cache_suffix}"
                )
            else:
                self.cache_path = osp.join(
                    osp.dirname(path), 
                    f"{task_name}_cache{cache_suffix}"
                )
        
        logger.info(f"缓存路径: {self.cache_path}")
        os.makedirs(self.cache_path, exist_ok=True)
        
        self.overwrite = overwrite
        self.filter_unvalid = filter_unvalid
        
        # 加载和处理数据
        self._load_and_process_data()
    
    def _load_and_process_data(self):
        """加载和处理数据"""
        # 加载原始数据
        logger.info(f"加载数据文件: {self.data_file}")
        with open(self.data_file, 'rb') as f:
            raw_data = pickle.load(f)

        # 提取图和属性数据
        if isinstance(raw_data, dict):
            if 'graphs' in raw_data and 'props' in raw_data:
                graphs_dict = raw_data['graphs']
                props_data = raw_data['props']

                # 处理pandas DataFrame格式的属性数据
                if hasattr(props_data, 'columns'):
                    # pandas DataFrame格式
                    if self.property_name in props_data.columns:
                        properties = props_data[self.property_name].values.tolist()
                    else:
                        # 尝试查找相似的属性名
                        prop_cols = list(props_data.columns)
                        matching_col = None
                        for col in prop_cols:
                            if self.property_name.lower() in str(col).lower() or str(col).lower() in self.property_name.lower():
                                matching_col = col
                                break

                        if matching_col:
                            properties = props_data[matching_col].values.tolist()
                            logger.warning(f"未找到属性'{self.property_name}'，使用'{matching_col}'")
                        else:
                            raise ValueError(f"未找到属性'{self.property_name}'，可用属性: {prop_cols}")

                elif isinstance(props_data, dict):
                    # 字典格式
                    if self.property_name in props_data:
                        properties = props_data[self.property_name]
                    else:
                        # 尝试查找相似的属性名
                        prop_keys = list(props_data.keys())
                        matching_key = None
                        for key in prop_keys:
                            if self.property_name.lower() in key.lower() or key.lower() in self.property_name.lower():
                                matching_key = key
                                break

                        if matching_key:
                            properties = props_data[matching_key]
                            logger.warning(f"未找到属性'{self.property_name}'，使用'{matching_key}'")
                        else:
                            raise ValueError(f"未找到属性'{self.property_name}'，可用属性: {prop_keys}")

                else:
                    raise ValueError(f"不支持的属性数据格式: {type(props_data)}")

                # 获取图的键列表
                graph_keys = list(graphs_dict.keys())

            else:
                raise ValueError(f"数据格式不正确，期望包含'graphs'和'props'键，实际键: {list(raw_data.keys())}")
        else:
            raise ValueError(f"数据格式不正确，期望为字典格式，实际类型: {type(raw_data)}")

        # 限制样本数量（用于测试）
        if self.max_samples is not None:
            graph_keys = graph_keys[:self.max_samples]
            properties = properties[:self.max_samples]
            logger.info(f"限制样本数量为: {self.max_samples}")

        self.num_samples = len(graph_keys)
        logger.info(f"总样本数: {self.num_samples}")

        # 直接使用图数据，不需要构建结构
        graph_cache_path = osp.join(self.cache_path, "graphs")
        if osp.exists(graph_cache_path) and not self.overwrite:
            logger.info("发现图缓存，从缓存加载")
            self.graphs = [
                osp.join(graph_cache_path, f"{i:010d}.pkl")
                for i in range(self.num_samples)
            ]
        else:
            logger.info("构建图缓存")
            os.makedirs(graph_cache_path, exist_ok=True)

            # 只有rank 0进程进行转换
            if dist.get_rank() == 0:
                # 保存图到缓存
                for i, key in enumerate(graph_keys):
                    graph = graphs_dict[key]
                    self.save_to_cache(
                        osp.join(graph_cache_path, f"{i:010d}.pkl"),
                        graph,
                    )
                logger.info(f"保存{self.num_samples}个图到缓存")

            # 同步所有进程
            if dist.is_initialized():
                dist.barrier()

            self.graphs = [
                osp.join(graph_cache_path, f"{i:010d}.pkl")
                for i in range(self.num_samples)
            ]

        # 保存属性数据
        property_cache_path = osp.join(self.cache_path, "properties")
        os.makedirs(property_cache_path, exist_ok=True)
        property_file = osp.join(property_cache_path, f"{self.property_name}.pkl")

        if not osp.exists(property_file) or self.overwrite:
            if dist.get_rank() == 0:
                self.save_to_cache(property_file, properties)
                logger.info(f"保存{self.num_samples}个{self.property_name}属性到缓存")
            if dist.is_initialized():
                dist.barrier()

        self.properties = self.load_from_cache(property_file)

        # 不需要结构数据
        self.structures = None

        # 过滤无效数据
        if self.filter_unvalid:
            self._filter_invalid_data()
    

    
    def _filter_invalid_data(self):
        """过滤无效数据"""
        valid_indices = []

        for i in range(self.num_samples):
            # 检查属性是否有效
            prop_value = self.properties[i]
            if prop_value is None or (isinstance(prop_value, float) and math.isnan(prop_value)):
                continue

            # 检查图是否有效
            try:
                graph = self.load_from_cache(self.graphs[i])
                if graph is None:
                    continue
                # 检查图是否有节点和边
                if hasattr(graph, 'num_nodes') and graph.num_nodes == 0:
                    continue
            except:
                continue

            valid_indices.append(i)

        if len(valid_indices) < self.num_samples:
            logger.warning(f"过滤掉{self.num_samples - len(valid_indices)}个无效样本")

            # 更新数据
            self.properties = [self.properties[i] for i in valid_indices]
            self.graphs = [self.graphs[i] for i in valid_indices]

            self.num_samples = len(valid_indices)
            logger.info(f"剩余有效样本数: {self.num_samples}")
    
    def save_to_cache(self, cache_path: str, data: Any):
        """保存数据到缓存"""
        with open(cache_path, "wb") as f:
            pickle.dump(data, f)
    
    def load_from_cache(self, cache_path: str):
        """从缓存加载数据"""
        if osp.exists(cache_path):
            with open(cache_path, "rb") as f:
                data = pickle.load(f)
            return data
        else:
            raise FileNotFoundError(f"缓存文件不存在: {cache_path}")
    
    def get_structure_array(self, structure):
        """将结构转换为数组格式"""
        atom_types = np.array([site.specie.Z for site in structure])
        lattice_parameters = structure.lattice.parameters
        lengths = np.array(lattice_parameters[:3], dtype="float32").reshape(1, 3)
        angles = np.array(lattice_parameters[3:], dtype="float32").reshape(1, 3)
        lattice = structure.lattice.matrix.astype("float32")
        
        structure_array = {
            "frac_coords": ConcatData(structure.frac_coords.astype("float32")),
            "cart_coords": ConcatData(structure.cart_coords.astype("float32")),
            "atom_types": ConcatData(atom_types),
            "lattice": ConcatData(lattice.reshape(1, 3, 3)),
            "lengths": ConcatData(lengths),
            "angles": ConcatData(angles),
            "num_atoms": ConcatData(np.array([tuple(atom_types.shape)[0]])),
        }
        return structure_array
    
    def __getitem__(self, idx: int):
        """获取单个样本"""
        data = {}

        # 获取图
        graph = self.load_from_cache(self.graphs[idx])
        data["graph"] = graph

        # 获取属性
        data[self.property_name] = np.array([self.properties[idx]]).astype("float32")

        # 添加ID
        data["id"] = idx

        # 应用变换
        if self.transforms is not None:
            data = self.transforms(data)

        return data
    
    def __len__(self):
        return self.num_samples
