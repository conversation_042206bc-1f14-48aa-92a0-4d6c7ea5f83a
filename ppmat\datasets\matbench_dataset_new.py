# Copyright (c) 2023 PaddlePaddle Authors. All Rights Reserved.

# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at

#     http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from __future__ import absolute_import
from __future__ import annotations

import math
import os
import os.path as osp
import pickle
from collections import defaultdict
from typing import Any
from typing import Callable
from typing import Dict
from typing import Optional

import numpy as np
import paddle.distributed as dist
from paddle.io import Dataset

from ppmat.datasets.build_structure import BuildStructure
from ppmat.datasets.custom_data_type import ConcatData
from ppmat.models import build_graph_converter
from ppmat.utils import logger
from ppmat.utils.misc import is_equal


class MatBenchDataset(Dataset):
    """MatBench Dataset Handler - 模仿MP2018Dataset风格

    这个类提供了加载和处理MatBench材料科学数据集的工具。
    数据集包含来自Materials Project数据库的无机材料计算属性。

    **数据集概述**
    - **来源**: MatBench基准数据集
    - **任务**: 形成能预测 (matbench_mp_e_form)
    - **格式**: 包含结构和属性的Pickle文件

    **数据格式**
    数据集以pickle格式存储，结构如下：

    ```python
    {
        "graphs": {
            0: <pymatgen.Structure>,    # 晶体结构
            1: <pymatgen.Structure>,
            # ...
        },
        "props": {
            "e_form": [                 # 形成能 (eV/atom)
                -1.8169,
                -1.8948,
                # ...
            ]
        }
    }
    ```

    Args:
        path (str): 数据集目录路径
        task_name (str): MatBench任务名称 (如 'matbench_mp_e_form_full')
        property_names (Optional[list[str]]): 要使用的属性名称列表
        build_structure_cfg (Dict, optional): 构建pymatgen结构的配置
        build_graph_cfg (Dict, optional): 从结构构建图的配置
        transforms (Optional[Callable], optional): 每个样本的预处理变换
        cache_path (Optional[str], optional): 处理数据的缓存路径
        overwrite (bool, optional): 覆盖现有缓存
        filter_unvalid (bool, optional): 是否过滤无效样本
        max_samples (Optional[int], optional): 最大样本数量。如果为None，加载所有样本
    """

    def __init__(
        self,
        path: str = "./data/matbench",
        task_name: str = "matbench_mp_e_form_full",
        property_names: Optional[list[str]] = None,
        build_structure_cfg: Dict = None,
        build_graph_cfg: Dict = None,
        transforms: Optional[Callable] = None,
        cache_path: Optional[str] = None,
        overwrite: bool = False,
        filter_unvalid: bool = True,
        max_samples: Optional[int] = None,
        **kwargs,  # for compatibility
    ):
        super().__init__()

        # 构建数据文件路径
        data_file = osp.join(path, f"{task_name}.pkl")
        if not osp.exists(data_file):
            raise FileNotFoundError(
                f"MatBench数据文件不存在: {data_file}. "
                f"请先下载MatBench数据集。"
            )

        self.path = data_file
        self.task_name = task_name
        self.max_samples = max_samples

        if isinstance(property_names, str):
            property_names = [property_names]

        if build_structure_cfg is None:
            build_structure_cfg = {
                "format": "pymatgen_structure",
                "primitive": False,
                "niggli": True,
                "num_cpus": 1,
            }
            logger.message(
                "build_structure_cfg未设置，使用默认配置: "
                f"{build_structure_cfg}"
            )

        self.property_names = property_names if property_names is not None else []
        self.build_structure_cfg = build_structure_cfg
        self.build_graph_cfg = build_graph_cfg
        self.transforms = transforms

        if cache_path is not None:
            self.cache_path = cache_path
        else:
            # 类似MP2018Dataset的缓存路径生成
            cache_suffix = f"_{max_samples}" if max_samples else ""
            self.cache_path = osp.join(
                osp.split(data_file)[0] + "_cache", 
                osp.splitext(osp.basename(data_file))[0] + cache_suffix
            )
        logger.info(f"缓存路径: {self.cache_path}")

        self.overwrite = overwrite
        self.filter_unvalid = filter_unvalid

        self.cache_exists = True if osp.exists(self.cache_path) else False
        
        # 读取原始数据 - 类似MP2018Dataset的流程
        self.row_data, self.num_samples = self.read_data(data_file)
        logger.info(f"从 {data_file} 加载 {self.num_samples} 个样本")
        
        # 读取属性数据
        self.property_data = self.read_property_data(self.row_data, self.property_names)

        # 缓存处理逻辑 - 类似MP2018Dataset
        if self.cache_exists and not overwrite:
            logger.warning(
                "缓存已启用。如果缓存文件存在，将自动读取并忽略当前设置。"
                "请确保缓存时使用的设置与当前设置匹配。"
            )
            try:
                build_structure_cfg_cache = self.load_from_cache(
                    osp.join(self.cache_path, "build_structure_cfg.pkl")
                )
                if is_equal(build_structure_cfg_cache, build_structure_cfg):
                    logger.info(
                        "缓存的build_structure_cfg配置与当前设置匹配。"
                        "重用之前生成的结构数据以优化性能。"
                    )
                else:
                    logger.warning(
                        "build_structure_cfg与缓存不同。将重新构建结构和图。"
                    )
                    overwrite = True
            except Exception as e:
                logger.warning(f"从缓存加载build_structure_cfg失败: {e}")
                overwrite = True

            if build_graph_cfg is not None and not overwrite:
                try:
                    build_graph_cfg_cache = self.load_from_cache(
                        osp.join(self.cache_path, "build_graph_cfg.pkl")
                    )
                    if is_equal(build_graph_cfg_cache, build_graph_cfg):
                        logger.info("缓存的build_graph_cfg配置匹配。重用缓存数据。")
                    else:
                        logger.warning("build_graph_cfg与缓存不同。将重新构建图。")
                        overwrite = True
                except Exception as e:
                    logger.warning(f"从缓存加载build_graph_cfg失败: {e}")
                    overwrite = True

        # 构建结构和图 - 类似MP2018Dataset
        self.build_structure = BuildStructure(**build_structure_cfg)
        self.structures = self.build_structures(overwrite)

        if build_graph_cfg is not None:
            self.graph_converter = build_graph_converter(build_graph_cfg)
            self.graphs = self.build_graphs(overwrite)
        else:
            self.graph_converter = None
            self.graphs = None

        # 保存缓存配置
        if not self.cache_exists or overwrite:
            self.save_to_cache(
                build_structure_cfg, osp.join(self.cache_path, "build_structure_cfg.pkl")
            )
            if build_graph_cfg is not None:
                self.save_to_cache(
                    build_graph_cfg, osp.join(self.cache_path, "build_graph_cfg.pkl")
                )

        # 过滤无效样本
        if filter_unvalid:
            self.filter_unvalid_by_property()

    def read_data(self, path: str):
        """读取MatBench数据 - 类似MP2018Dataset.read_data
        
        Args:
            path (str): 数据文件路径
        """
        logger.info(f"读取MatBench数据: {path}")
        
        with open(path, 'rb') as f:
            raw_data = pickle.load(f)
        
        # 验证数据格式
        if not isinstance(raw_data, dict) or 'graphs' not in raw_data or 'props' not in raw_data:
            raise ValueError(
                f"无效的MatBench数据格式。期望包含'graphs'和'props'键的字典。"
            )
        
        graphs_data = raw_data['graphs']
        
        # 转换为一致的格式
        if isinstance(graphs_data, dict):
            # 将字典索引转换为列表
            indices = sorted(graphs_data.keys(), key=lambda x: int(x))
            structures = [graphs_data[idx] for idx in indices]
        else:
            structures = list(graphs_data)
        
        # 应用max_samples限制
        if self.max_samples is not None:
            structures = structures[:self.max_samples]
            logger.info(f"限制样本数量为: {self.max_samples} (总共 {len(structures)} 个)")
        
        num_samples = len(structures)
        
        # 准备行数据（结构列表）
        row_data = structures
        
        return row_data, num_samples

    def read_property_data(self, structures, property_names):
        """读取属性数据 - 类似MP2018Dataset.read_property_data

        Args:
            structures: 结构列表
            property_names: 属性名称列表
        """
        # 重新加载pickle文件以获取属性
        with open(self.path, 'rb') as f:
            data = pickle.load(f)

        props_data = data['props']

        # 处理不同的属性数据格式
        property_data = {}

        for prop_name in property_names:
            if isinstance(props_data, dict):
                if prop_name in props_data:
                    prop_values = props_data[prop_name]
                    if isinstance(prop_values, dict):
                        # 将字典转换为列表
                        indices = sorted(prop_values.keys(), key=lambda x: int(x))
                        prop_list = [prop_values[idx] for idx in indices]
                    else:
                        prop_list = list(prop_values)

                    # 应用max_samples限制
                    if self.max_samples is not None:
                        prop_list = prop_list[:self.max_samples]

                    property_data[prop_name] = prop_list
                else:
                    raise ValueError(f"属性 '{prop_name}' 在数据中未找到")
            elif hasattr(props_data, 'columns'):
                # pandas DataFrame格式
                if prop_name in props_data.columns:
                    prop_list = props_data[prop_name].values.tolist()
                    if self.max_samples is not None:
                        prop_list = prop_list[:self.max_samples]
                    property_data[prop_name] = prop_list
                else:
                    raise ValueError(f"属性 '{prop_name}' 在DataFrame中未找到")
            else:
                raise ValueError(f"不支持的属性数据格式: {type(props_data)}")

        return property_data

    def filter_unvalid_by_property(self):
        """过滤无效属性的样本 - 类似MP2018Dataset.filter_unvalid_by_property"""
        for property_name in self.property_names:
            data = self.property_data[property_name]
            reserve_idx = []
            for i, data_item in enumerate(data):
                if isinstance(data_item, str) or (data_item is not None and not math.isnan(data_item)):
                    reserve_idx.append(i)

            # 更新所有数据
            for key in self.property_data.keys():
                self.property_data[key] = [
                    self.property_data[key][i] for i in reserve_idx
                ]

            self.row_data = [self.row_data[i] for i in reserve_idx]
            self.structures = [self.structures[i] for i in reserve_idx]
            if self.graphs is not None:
                self.graphs = [self.graphs[i] for i in reserve_idx]

            logger.warning(
                f"过滤出 {len(reserve_idx)} 个有效属性样本: {property_name}"
            )

        self.num_samples = len(self.row_data)
        logger.warning(f"过滤后剩余 {self.num_samples} 个样本。")

    # 以下方法从MP2018Dataset复制，保持一致的接口
    def build_structures(self, overwrite: bool = False):
        """构建结构 - 从MP2018Dataset复制"""
        structure_cache_path = osp.join(self.cache_path, "structures")
        if osp.exists(structure_cache_path) and not overwrite:
            logger.info("发现结构缓存，从缓存加载")
            structures = [
                osp.join(structure_cache_path, f"{i:010d}.pkl")
                for i in range(self.num_samples)
            ]
        else:
            logger.info("构建结构")
            os.makedirs(structure_cache_path, exist_ok=True)

            # 只有rank 0进程进行转换
            if dist.get_rank() == 0:
                # 保存结构到缓存
                for i, structure in enumerate(self.row_data):
                    # MatBench数据已经是pymatgen结构，直接保存
                    structure_file = osp.join(structure_cache_path, f"{i:010d}.pkl")
                    with open(structure_file, 'wb') as f:
                        pickle.dump(structure, f)

                    if (i + 1) % 1000 == 0:
                        logger.info(f"已处理 {i + 1}/{self.num_samples} 个结构")

            # 等待所有进程
            dist.barrier()

            structures = [
                osp.join(structure_cache_path, f"{i:010d}.pkl")
                for i in range(self.num_samples)
            ]

        return structures

    def build_graphs(self, overwrite: bool = False):
        """构建图 - 从MP2018Dataset复制并修改"""
        graph_cache_path = osp.join(self.cache_path, "graphs")
        if osp.exists(graph_cache_path) and not overwrite:
            logger.info("发现图缓存，从缓存加载")
            graphs = [
                osp.join(graph_cache_path, f"{i:010d}.pkl")
                for i in range(self.num_samples)
            ]
        else:
            logger.info("构建图")
            os.makedirs(graph_cache_path, exist_ok=True)

            # 只有rank 0进程进行转换
            if dist.get_rank() == 0:
                for i, structure_path in enumerate(self.structures):
                    # 加载结构
                    with open(structure_path, 'rb') as f:
                        structure = pickle.load(f)

                    # 转换为图
                    graph = self.graph_converter(structure)

                    # 保存图到缓存
                    graph_file = osp.join(graph_cache_path, f"{i:010d}.pkl")
                    with open(graph_file, 'wb') as f:
                        pickle.dump(graph, f)

                    if (i + 1) % 100 == 0:
                        logger.info(f"已处理 {i + 1}/{self.num_samples} 个图")

            # 等待所有进程
            dist.barrier()

            graphs = [
                osp.join(graph_cache_path, f"{i:010d}.pkl")
                for i in range(self.num_samples)
            ]

        return graphs

    def load_from_cache(self, cache_path: str):
        """从缓存加载 - 从MP2018Dataset复制"""
        with open(cache_path, 'rb') as f:
            return pickle.load(f)

    def save_to_cache(self, data: Any, cache_path: str):
        """保存到缓存 - 从MP2018Dataset复制"""
        os.makedirs(osp.dirname(cache_path), exist_ok=True)
        with open(cache_path, 'wb') as f:
            pickle.dump(data, f)

    def __len__(self):
        """返回数据集大小"""
        return self.num_samples

    def __getitem__(self, idx):
        """获取单个样本"""
        if self.graphs is not None:
            # 加载图
            with open(self.graphs[idx], 'rb') as f:
                graph = pickle.load(f)
        else:
            # 加载结构
            with open(self.structures[idx], 'rb') as f:
                structure = pickle.load(f)
            graph = structure

        # 构建样本数据
        sample = {"graph": graph}

        # 添加属性数据
        for prop_name in self.property_names:
            sample[prop_name] = self.property_data[prop_name][idx]

        # 应用变换
        if self.transforms is not None:
            sample = self.transforms(sample)

        return sample
