#!/usr/bin/env python3
"""
测试修复后的配置文件
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_config_loading():
    """测试配置文件加载"""
    print("🔍 测试配置文件加载...")
    
    try:
        from omegaconf import OmegaConf
        
        config_path = Path(__file__).parent / "test_config_1000.yaml"
        config = OmegaConf.load(config_path)
        
        print(f"✅ 配置文件加载成功")
        print(f"  数据分割比例: {config.Dataset.split_dataset_ratio}")
        print(f"  数据集类: {config.Dataset.dataset.__class_name__}")
        print(f"  最大样本数: {config.Dataset.dataset.__init_params__.max_samples}")
        
        return config
        
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_dataset_creation():
    """测试数据集创建"""
    print("\n🔍 测试数据集创建...")
    
    try:
        from ppmat.datasets import build_dataloader
        from omegaconf import OmegaConf
        
        config_path = Path(__file__).parent / "test_config_1000.yaml"
        config = OmegaConf.load(config_path)
        
        # 测试数据集构建
        dataset_cfg = config.Dataset
        
        print(f"  数据集配置: {dataset_cfg.dataset.__class_name__}")
        print(f"  分割比例: {dataset_cfg.split_dataset_ratio}")
        
        # 尝试构建数据加载器
        loader_dict = build_dataloader(dataset_cfg)
        
        print(f"✅ 数据加载器构建成功")
        print(f"  训练集: {loader_dict.get('train', 'None')}")
        print(f"  验证集: {loader_dict.get('val', 'None')}")
        print(f"  测试集: {loader_dict.get('test', 'None')}")
        
        # 测试数据加载器大小
        if loader_dict.get('train'):
            train_size = len(loader_dict['train'].dataset)
            print(f"  训练集大小: {train_size}")
        
        if loader_dict.get('val'):
            val_size = len(loader_dict['val'].dataset)
            print(f"  验证集大小: {val_size}")
        
        if loader_dict.get('test'):
            test_size = len(loader_dict['test'].dataset)
            print(f"  测试集大小: {test_size}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据集创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_loading():
    """测试批次加载"""
    print("\n🔍 测试批次加载...")
    
    try:
        from ppmat.datasets import build_dataloader
        from omegaconf import OmegaConf
        
        config_path = Path(__file__).parent / "test_config_1000.yaml"
        config = OmegaConf.load(config_path)
        
        # 构建数据加载器
        dataset_cfg = config.Dataset
        loader_dict = build_dataloader(dataset_cfg)
        
        # 测试训练集批次
        if loader_dict.get('train'):
            train_loader = loader_dict['train']
            print(f"  训练集批次数: {len(train_loader)}")
            
            # 获取第一个批次
            for batch in train_loader:
                print(f"  训练批次键: {list(batch.keys())}")
                for key, value in batch.items():
                    if hasattr(value, 'shape'):
                        print(f"    {key}: 形状={value.shape}")
                    elif isinstance(value, list):
                        print(f"    {key}: 列表长度={len(value)}")
                    else:
                        print(f"    {key}: 类型={type(value)}")
                break  # 只测试第一个批次
        
        # 测试验证集批次
        if loader_dict.get('val'):
            val_loader = loader_dict['val']
            print(f"  验证集批次数: {len(val_loader)}")
            
            # 获取第一个批次
            for batch in val_loader:
                print(f"  验证批次键: {list(batch.keys())}")
                break  # 只测试第一个批次
        
        print("✅ 批次加载测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 批次加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 修复后配置文件测试")
    print("=" * 40)
    
    # 检查数据文件
    data_file = project_root / "data" / "matbench" / "matbench_mp_e_form_full.pkl"
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    tests = [
        ("配置文件加载", test_config_loading),
        ("数据集创建", test_dataset_creation),
        ("批次加载", test_batch_loading),
    ]
    
    all_passed = True
    
    for i, (test_name, test_func) in enumerate(tests, 1):
        print(f"\n测试 {i}/{len(tests)}: {test_name}")
        print("-" * 30)
        
        if not test_func():
            all_passed = False
            break  # 如果某个测试失败，停止后续测试
    
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 所有测试通过!")
        print("修复后的配置文件可以正常使用")
        print("\n现在可以运行训练:")
        print("  python property_prediction/train.py -c test_matbench/test_config_1000.yaml")
    else:
        print("❌ 部分测试失败")
        print("请检查错误信息并修复问题")

if __name__ == "__main__":
    main()
