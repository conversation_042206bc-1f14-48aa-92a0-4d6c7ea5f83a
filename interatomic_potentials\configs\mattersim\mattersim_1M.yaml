Global:
  do_train: True
  do_eval: False
  do_test: False

  label_names: ['energy', 'force']

  energy_key: 'energy'
  force_key: 'force'
  stress_key: null # 'stress' # high level water data not support stress


  graph_converter:
    __class_name__: M3GNetGraphConvertor
    __init_params__: {}

  prim_eager_enabled: True
  prim_backward_white_list: ['stack_grad', 'assign_grad']


Trainer:
  # Max epochs to train
  max_epochs: 20
  # Random seed
  seed: 42
  # Save path for checkpoints and logs
  output_dir: ./output/mattersim_1M
  # Save frequency [epoch], for example, save_freq=10 means save checkpoints every 10 epochs
  save_freq: 5 # set 0 to disable saving during training
  # Logging frequency [step], for example, log_freq=10 means log every 10 steps
  log_freq: 1 # log frequency [step]

  # Start evaluation epoch, for example, start_eval_epoch=10 means start evaluation from epoch 10
  start_eval_epoch: 1
  # Evaluation frequency [epoch], for example, eval_freq=1 means evaluate every 1 epoch
  eval_freq: 1 # set 0 to disable evaluation during training
  # Pretrained model path, if null, no pretrained model will be loaded
  pretrained_model_path: "https://paddle-org.bj.bcebos.com/paddlematerial/checkpoints/interatomic_potentials/mattersim/mattersim_1M.zip"
  # Pretrained weight name, will be used when pretrained_model_path is a directory
  pretrained_weight_name: mattersim-v1.0.0-1M_model.pdparams #'latest.pdparams'
  # Resume from checkpoint path, useful for resuming training
  resume_from_checkpoint: null
  # whether use automatic mixed precision
  use_amp: False
  # automatic mixed precision level
  amp_level: 'O1'
  # whether run a model on no_grad mode during evaluation, useful for saving memory
  # If the model contains higher-order derivatives in the forward, it should be set to
  # False
  eval_with_no_grad: False
  # gradient accumulation steps, for example, gradient_accumulation_steps=2 means
  # gradient accumulation every 2 forward steps
  # Note:
  # one complete step  = gradient_accumulation_steps * forward steps + backward steps
  gradient_accumulation_steps: 1

  # best metric indicator, you can choose from "train_loss", "eval_loss", "train_metric", "eval_metric"
  best_metric_indicator: 'eval_metric' # "train_loss", "eval_loss", "train_metric", "eval_metric"
  # The name of the best metric, since you may have multiple metrics, such as "mae", "rmse", "mape"
  name_for_best_metric: "energy"
  # The metric whether is better when it is greater
  greater_is_better: False

  # compute metric during training or evaluation
  compute_metric_during_train: True # True: the metric will be calculated on train dataset
  metric_strategy_during_eval: 'epoch' # step or epoch, compute metric after step or epoch, if set to 'step', the metric will be calculated after every step, else after epoch

  # whether use visualdl, wandb, tensorboard to log
  use_visualdl: False
  use_wandb: False
  use_tensorboard: False


Model:
  __class_name__: M3GNet
  __init_params__:
    num_blocks: 3
    units: 128
    max_l:  4
    max_n: 4
    cutoff: 5.0
    max_z:  94
    threebody_cutoff:  4.0
    energy_key: ${Global.energy_key}
    force_key: ${Global.force_key}
    stress_key: ${Global.stress_key}
    loss_type: 'smooth_l1_loss'
    huber_loss_delta: 0.01
    loss_weights_dict:
      energy: 1.0
      force: 1.0
      stress: 0.1


Predict:
  graph_converter: ${Global.graph_converter}
  eval_with_no_grad: False
