#!/usr/bin/env python3
"""
MatBench数据集下载脚本
下载完整的MatBench数据集，不做任何修改
"""
import os
import pickle
from pathlib import Path

def check_dependencies():
    """检查依赖包"""
    try:
        import matbench
        print("✅ matbench 已安装")
        return True
    except ImportError:
        print("❌ matbench 未安装")
        print("请运行: pip install matbench")
        return False

def download_matbench_e_form():
    """下载MatBench形成能数据集"""
    
    if not check_dependencies():
        return False
    
    from matbench.bench import MatbenchBenchmark
    
    # 创建数据目录
    data_dir = Path("../data/matbench")
    data_dir.mkdir(parents=True, exist_ok=True)
    
    print("开始下载MatBench形成能数据集...")
    print("注意: 首次下载可能需要10-20分钟，请耐心等待")
    
    try:
        # 初始化MatBench基准测试
        mb = MatbenchBenchmark(autoload=False)
        
        # 下载matbench_mp_e_form任务数据
        task_name = "matbench_mp_e_form"
        print(f"正在下载 {task_name} 数据集...")
        
        # 加载任务数据
        task = mb.matbench_mp_e_form
        task.load()
        
        # 获取完整数据
        df = task.df
        print(f"完整数据集大小: {len(df)} 个样本")
        
        # 转换为标准格式
        print("正在转换数据格式...")
        graphs_dict = {}
        props_dict = {"e_form": []}
        
        for i, (idx, row) in enumerate(df.iterrows()):
            # 存储结构信息（使用pymatgen Structure对象）
            graphs_dict[i] = row['structure']
            # 存储属性值
            props_dict["e_form"].append(row['e_form'])
            
            if (i + 1) % 10000 == 0:
                print(f"已处理 {i + 1}/{len(df)} 个样本 ({(i+1)/len(df)*100:.1f}%)")
        
        # 保存完整数据集
        full_data = {
            'graphs': graphs_dict,
            'props': props_dict
        }
        
        output_file_full = data_dir / "matbench_mp_e_form_full.pkl"
        print(f"正在保存完整数据集到: {output_file_full}")
        
        with open(output_file_full, 'wb') as f:
            pickle.dump(full_data, f)
        
        print("✅ 完整数据集保存完成!")
        
        # 显示数据统计信息
        e_form_values = props_dict["e_form"]
        print(f"\n📊 数据统计:")
        print(f"  总样本数: {len(graphs_dict)}")
        print(f"  形成能范围: {min(e_form_values):.3f} ~ {max(e_form_values):.3f} eV/atom")
        print(f"  平均形成能: {sum(e_form_values)/len(e_form_values):.3f} eV/atom")
        
        print(f"\n📁 数据文件位置:")
        print(f"  {output_file_full}")
        
        return True
        
    except Exception as e:
        print(f"✗ 下载失败: {e}")
        print("请检查网络连接或稍后重试")
        return False

def main():
    """主函数"""
    print("🎯 MatBench数据集下载工具")
    print("=" * 50)
    
    print("这个脚本将:")
    print("1. 下载完整的MatBench形成能数据集")
    print("2. 保存为标准格式的pickle文件")
    print("3. 不对数据做任何修改或子集化")
    
    choice = input("\n是否继续下载? (y/n): ").lower().strip()
    if choice != 'y':
        print("取消下载")
        return
    
    if download_matbench_e_form():
        print("\n🎉 下载完成!")
        print("现在可以使用MatBenchDataset类加载数据了")
        print("示例:")
        print("  dataset = MatBenchDataset(")
        print("      path='./data/matbench',")
        print("      task_name='matbench_mp_e_form_full',")
        print("      max_samples=1000  # 只使用前1000个样本")
        print("  )")
    else:
        print("\n❌ 下载失败")

if __name__ == "__main__":
    main()
