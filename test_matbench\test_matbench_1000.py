#!/usr/bin/env python3
"""
MatBench 1000样本测试脚本
"""
import os
import sys
import subprocess
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_data_file():
    """检查数据文件是否存在"""
    data_file = project_root / "data" / "matbench" / "matbench_mp_e_form_full.pkl"
    
    if data_file.exists():
        print(f"✅ 数据文件存在: {data_file}")
        return True
    else:
        print(f"❌ 数据文件不存在: {data_file}")
        print("请先运行下载脚本: python test_matbench/download_matbench.py")
        return False

def test_dataset_loading():
    """测试数据集加载"""
    print("\n🔍 测试数据集加载...")
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        
        # 测试加载1000样本
        dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_full",
            property_name="e_form",
            max_samples=1000,
            overwrite=True  # 强制重新处理以确保测试
        )
        
        print(f"✅ 数据集加载成功!")
        print(f"  总样本数: {dataset.num_samples}")
        print(f"  任务名称: {dataset.task_name}")
        print(f"  属性名称: {dataset.property_name}")
        
        # 测试获取单个样本
        sample = dataset[0]
        print(f"  样本结构: {list(sample.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据集加载失败: {e}")
        return False

def test_training():
    """测试训练过程"""
    print("\n🚀 开始测试训练...")
    
    config_file = Path(__file__).parent / "test_config_1000.yaml"
    
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    # 切换到项目根目录
    os.chdir(project_root)
    
    cmd = f"python property_prediction/train.py -c {config_file}"
    print(f"执行命令: {cmd}")
    print("预计训练时间: 1-2小时")
    print("=" * 60)
    
    try:
        start_time = time.time()
        
        # 实时显示输出
        process = subprocess.Popen(
            cmd,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # 实时打印输出
        for line in process.stdout:
            print(line.rstrip())
        
        process.wait()
        
        end_time = time.time()
        duration = end_time - start_time
        
        if process.returncode == 0:
            print(f"\n✅ 训练完成! 用时: {duration/3600:.1f} 小时")
            return True
        else:
            print(f"\n❌ 训练失败! 返回码: {process.returncode}")
            return False
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断训练")
        return False
    except Exception as e:
        print(f"\n❌ 训练异常: {e}")
        return False

def analyze_results():
    """分析训练结果"""
    print("\n📊 分析训练结果...")
    
    # 查找输出目录
    output_dir = project_root / "test_matbench" / "output" / "comformer_1000_samples"
    
    if not output_dir.exists():
        print(f"❌ 输出目录不存在: {output_dir}")
        return False
    
    # 查找最新的训练目录
    train_dirs = [d for d in output_dir.iterdir() if d.is_dir()]
    if not train_dirs:
        print("❌ 未找到训练输出目录")
        return False
    
    latest_dir = max(train_dirs, key=lambda x: x.stat().st_mtime)
    print(f"📁 最新训练目录: {latest_dir}")
    
    # 检查日志文件
    log_file = latest_dir / "run.log"
    if log_file.exists():
        print(f"📄 日志文件: {log_file}")
        
        # 提取最终结果
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 查找最终评估结果
            final_results = []
            for line in reversed(lines[-50:]):
                if "Eval:" in line and "e_form" in line:
                    final_results.append(line.strip())
                    if len(final_results) >= 3:
                        break
            
            if final_results:
                print("\n📈 最终训练结果:")
                for result in reversed(final_results):
                    print(f"  {result}")
            
        except Exception as e:
            print(f"⚠️ 读取日志失败: {e}")
    
    # 检查模型文件
    checkpoints_dir = latest_dir / "checkpoints"
    if checkpoints_dir.exists():
        print(f"\n💾 模型检查点: {checkpoints_dir}")
        
        # 列出可用的检查点
        checkpoints = [item.name for item in checkpoints_dir.iterdir() if item.is_dir()]
        
        if checkpoints:
            print("  可用检查点:")
            for cp in sorted(checkpoints):
                print(f"    - {cp}")
    
    return True

def test_prediction():
    """测试预测功能"""
    print("\n🔮 测试预测功能...")
    
    # 查找最新的模型
    output_dir = project_root / "test_matbench" / "output" / "comformer_1000_samples"
    
    if not output_dir.exists():
        print("❌ 未找到训练输出目录")
        return False
    
    train_dirs = [d for d in output_dir.iterdir() if d.is_dir()]
    if not train_dirs:
        print("❌ 未找到训练目录")
        return False
    
    latest_dir = max(train_dirs, key=lambda x: x.stat().st_mtime)
    best_model_path = latest_dir / "checkpoints" / "best"
    
    if not best_model_path.exists():
        print(f"❌ 最佳模型不存在: {best_model_path}")
        return False
    
    # 运行预测
    config_path = Path(__file__).parent / "test_config_1000.yaml"
    cif_path = project_root / "property_prediction" / "example_data" / "cifs"
    
    cmd = f"python property_prediction/predict.py --config_path='{config_path}' --weights_path='{best_model_path}' --cif_file_path='{cif_path}'"
    
    try:
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=True,
            text=True,
            timeout=120,
            cwd=project_root
        )
        
        if result.returncode == 0:
            print("✅ 预测测试成功!")
            print("预测结果:")
            print(result.stdout)
            return True
        else:
            print("❌ 预测测试失败!")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 预测测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🎯 MatBench 1000样本测试")
    print("=" * 50)
    
    steps = [
        ("检查数据文件", check_data_file),
        ("测试数据集加载", test_dataset_loading),
        ("开始训练", test_training),
        ("分析结果", analyze_results),
        ("测试预测", test_prediction)
    ]
    
    for i, (step_name, step_func) in enumerate(steps, 1):
        print(f"\n步骤 {i}/5: {step_name}")
        print("-" * 40)
        
        if not step_func():
            print(f"❌ 步骤 {i} 失败")
            
            if i <= 2:  # 前2步失败则停止
                print("关键步骤失败，停止测试")
                return False
            else:  # 后续步骤失败可以继续
                choice = input("是否继续下一步? (y/n): ").lower().strip()
                if choice != 'y':
                    break
        
        if i < len(steps) and i != 3:  # 训练步骤不需要额外暂停
            input("\n按回车键继续下一步...")
    
    print("\n🎉 测试完成!")
    print("=" * 50)
    print("📋 总结:")
    print("• 使用完整MatBench数据集（未修改）")
    print("• 在数据集类中限制为前1000个样本")
    print("• 训练了Comformer模型")
    print("• 预期性能: MAE 0.08-0.12 eV/atom")
    
    print("\n🚀 下一步建议:")
    print("• 调整max_samples参数测试不同规模")
    print("• 对比不同样本数量的性能")
    print("• 在更多CIF文件上测试预测")

if __name__ == "__main__":
    main()
