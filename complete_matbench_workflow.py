#!/usr/bin/env python3
"""
完整的MatBench工作流程
"""
import os
import sys
import subprocess
import time

def run_command(cmd, description, timeout=300):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"执行: {description}")
    print(f"命令: {cmd}")
    print('='*60)
    
    try:
        start_time = time.time()
        result = subprocess.run(cmd, shell=True, timeout=timeout)
        end_time = time.time()
        
        duration = end_time - start_time
        print(f"执行时间: {duration:.2f} 秒")
        
        if result.returncode == 0:
            print(f"OK {description} 成功完成")
            return True
        else:
            print(f"FAIL {description} 失败，返回码: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"TIMEOUT {description} 超时 ({timeout}秒)")
        return False
    except Exception as e:
        print(f"ERROR 执行 {description} 时出错: {e}")
        return False

def check_prerequisites():
    """检查先决条件"""
    print("检查先决条件...")
    
    # 检查原始数据文件
    original_data = "data/matbench/matbench_mp_e_form_processed.pkl"
    if not os.path.exists(original_data):
        print(f"ERROR: 原始数据文件不存在: {original_data}")
        return False
    print(f"OK: 原始数据文件存在")
    
    # 检查训练脚本
    train_script = "property_prediction/train.py"
    if not os.path.exists(train_script):
        print(f"ERROR: 训练脚本不存在: {train_script}")
        return False
    print(f"OK: 训练脚本存在")
    
    # 检查配置文件
    config_file = "property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml"
    if not os.path.exists(config_file):
        print(f"ERROR: 配置文件不存在: {config_file}")
        return False
    print(f"OK: 配置文件存在")
    
    return True

def main():
    """主工作流程"""
    print("MatBench完整工作流程")
    print("="*60)
    
    # 检查先决条件
    if not check_prerequisites():
        print("FAIL: 先决条件检查失败")
        return False
    
    # 工作流程步骤
    steps = [
        # 1. 数据准备和验证
        ("python test_data_preparation.py", "数据准备和验证", 60),
        
        # 2. 训练准备测试
        ("python test_matbench_training_simple.py", "训练准备测试", 120),
        
        # 3. 完整数据集测试（可选）
        ("python test_matbench_dataset.py", "完整数据集测试", 180),
    ]
    
    passed = 0
    total = len(steps)
    
    for cmd, description, timeout in steps:
        if run_command(cmd, description, timeout):
            passed += 1
        else:
            print(f"\nFAIL 工作流程在 '{description}' 步骤失败")
            break
    
    print(f"\n{'='*60}")
    print(f"工作流程总结: {passed}/{total} 个步骤通过")
    print('='*60)
    
    if passed == total:
        print("SUCCESS 所有准备步骤完成！")
        print("\n现在可以开始训练:")
        print("="*40)
        print("选项1 - 快速训练测试 (推荐):")
        print("python property_prediction/train.py -c property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml")
        print("\n选项2 - 使用训练脚本:")
        print("python run_matbench_training.py")
        print("="*40)
        
        # 询问是否立即开始训练
        try:
            response = input("\n是否立即开始训练? (y/N): ").strip().lower()
            if response in ['y', 'yes']:
                print("\n开始训练...")
                train_cmd = "python property_prediction/train.py -c property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml"
                return run_command(train_cmd, "MatBench训练", 1800)  # 30分钟超时
            else:
                print("训练已跳过。您可以稍后手动运行训练命令。")
                return True
        except KeyboardInterrupt:
            print("\n用户中断，训练已跳过。")
            return True
    else:
        print("FAIL 工作流程未完成，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
