#!/usr/bin/env python3
"""
测试数据分割功能
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_data_split():
    """测试数据分割功能"""
    print("🔍 测试数据分割功能...")
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        
        # 测试训练集：前8个样本
        train_dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_full",
            property_names=["e_form"],
            max_samples=8,
            skip_samples=0,
            overwrite=False,
            build_graph_cfg=None
        )
        
        # 测试验证集：第9-10个样本
        val_dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_full",
            property_names=["e_form"],
            max_samples=10,
            skip_samples=8,
            overwrite=False,
            build_graph_cfg=None
        )
        
        # 测试测试集：第11-12个样本
        test_dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_full",
            property_names=["e_form"],
            max_samples=12,
            skip_samples=10,
            overwrite=False,
            build_graph_cfg=None
        )
        
        print(f"✅ 数据集创建成功!")
        print(f"  训练集样本数: {len(train_dataset)}")
        print(f"  验证集样本数: {len(val_dataset)}")
        print(f"  测试集样本数: {len(test_dataset)}")
        
        # 检查数据是否不重叠
        train_e_forms = [train_dataset[i]['e_form'] for i in range(len(train_dataset))]
        val_e_forms = [val_dataset[i]['e_form'] for i in range(len(val_dataset))]
        test_e_forms = [test_dataset[i]['e_form'] for i in range(len(test_dataset))]
        
        print(f"\n训练集e_form值: {train_e_forms}")
        print(f"验证集e_form值: {val_e_forms}")
        print(f"测试集e_form值: {test_e_forms}")
        
        # 检查是否有重叠
        train_set = set(train_e_forms)
        val_set = set(val_e_forms)
        test_set = set(test_e_forms)
        
        train_val_overlap = train_set & val_set
        train_test_overlap = train_set & test_set
        val_test_overlap = val_set & test_set
        
        if not train_val_overlap and not train_test_overlap and not val_test_overlap:
            print("✅ 数据分割正确，没有重叠")
            return True
        else:
            print("❌ 数据分割有重叠:")
            if train_val_overlap:
                print(f"  训练-验证重叠: {train_val_overlap}")
            if train_test_overlap:
                print(f"  训练-测试重叠: {train_test_overlap}")
            if val_test_overlap:
                print(f"  验证-测试重叠: {val_test_overlap}")
            return False
        
    except Exception as e:
        print(f"❌ 数据分割测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_empty_validation():
    """测试验证集为空的情况"""
    print("\n🔍 测试验证集为空的情况...")
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        
        # 创建一个会导致验证集为空的配置
        empty_dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_full",
            property_names=["e_form"],
            max_samples=5,  # 最大5个样本
            skip_samples=10,  # 跳过前10个样本，导致为空
            overwrite=False,
            build_graph_cfg=None
        )
        
        print(f"空数据集样本数: {len(empty_dataset)}")
        
        if len(empty_dataset) == 0:
            print("✅ 正确处理了空数据集情况")
            return True
        else:
            print("❌ 空数据集处理不正确")
            return False
        
    except Exception as e:
        print(f"❌ 空数据集测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 数据分割功能测试")
    print("=" * 40)
    
    # 检查数据文件
    data_file = project_root / "data" / "matbench" / "matbench_mp_e_form_full.pkl"
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    tests = [
        ("数据分割测试", test_data_split),
        ("空验证集测试", test_empty_validation),
    ]
    
    all_passed = True
    
    for i, (test_name, test_func) in enumerate(tests, 1):
        print(f"\n测试 {i}/{len(tests)}: {test_name}")
        print("-" * 30)
        
        if not test_func():
            all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 数据分割测试通过!")
        print("现在可以使用修复后的配置文件进行训练了")
        print("\n建议的数据分割:")
        print("• 训练集: 前800个样本 (max_samples=800, skip_samples=0)")
        print("• 验证集: 第801-900个样本 (max_samples=1000, skip_samples=800)")
        print("• 测试集: 第901-1000个样本 (max_samples=1000, skip_samples=900)")
    else:
        print("❌ 数据分割测试失败")

if __name__ == "__main__":
    main()
