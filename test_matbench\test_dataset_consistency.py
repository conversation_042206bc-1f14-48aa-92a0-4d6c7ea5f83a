#!/usr/bin/env python3
"""
测试MatBenchDataset的数据一致性和批处理兼容性
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_dataset_loading():
    """测试数据集加载"""
    print("🔍 测试数据集加载...")
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        
        # 测试加载50个样本
        dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_full",
            property_names=["e_form"],
            max_samples=50,
            overwrite=True,
            build_graph_cfg={
                "__class_name__": "ComformerGraphConverter",
                "__init_params__": {
                    "cutoff": 5.0,
                    "num_cpus": 1,
                    "atom_features": "cgcnn",
                    "max_neighbors": 25
                }
            }
        )
        
        print(f"✅ 数据集加载成功!")
        print(f"  样本数: {len(dataset)}")
        print(f"  属性名: {dataset.property_names}")
        
        return dataset
        
    except Exception as e:
        print(f"❌ 数据集加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_sample_consistency(dataset):
    """测试样本一致性"""
    print("\n🔍 测试样本一致性...")
    
    if dataset is None:
        print("❌ 数据集为空，跳过测试")
        return False
    
    try:
        # 测试前10个样本
        samples = []
        for i in range(min(10, len(dataset))):
            sample = dataset[i]
            samples.append(sample)
            print(f"  样本 {i}: 键={list(sample.keys())}")
        
        # 检查所有样本是否有相同的键
        if samples:
            first_keys = set(samples[0].keys())
            all_consistent = all(set(sample.keys()) == first_keys for sample in samples)
            
            if all_consistent:
                print("✅ 所有样本的键一致")
            else:
                print("❌ 样本的键不一致")
                for i, sample in enumerate(samples):
                    print(f"    样本 {i}: {set(sample.keys())}")
                return False
            
            # 检查数据类型一致性
            for key in first_keys:
                types = [type(sample[key]) for sample in samples]
                if len(set(str(t) for t in types)) == 1:
                    print(f"✅ 键 '{key}' 的数据类型一致: {types[0]}")
                else:
                    print(f"❌ 键 '{key}' 的数据类型不一致: {types}")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ 样本一致性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_processing(dataset):
    """测试批处理"""
    print("\n🔍 测试批处理...")
    
    if dataset is None:
        print("❌ 数据集为空，跳过测试")
        return False
    
    try:
        from paddle.io import DataLoader
        from ppmat.datasets.collate_fn import DefaultCollator
        
        # 创建数据加载器
        dataloader = DataLoader(
            dataset,
            batch_size=4,
            shuffle=False,
            num_workers=0,  # 避免多进程问题
            collate_fn=DefaultCollator(),
            drop_last=False
        )
        
        print(f"  数据加载器创建成功，批次大小: 4")
        
        # 测试前几个批次
        batch_count = 0
        for batch in dataloader:
            batch_count += 1
            print(f"  批次 {batch_count}: 键={list(batch.keys())}")
            
            # 检查批次数据
            for key, value in batch.items():
                if hasattr(value, 'shape'):
                    print(f"    {key}: 形状={value.shape}, 类型={type(value)}")
                elif isinstance(value, list):
                    print(f"    {key}: 列表长度={len(value)}, 类型={type(value)}")
                else:
                    print(f"    {key}: 值={value}, 类型={type(value)}")
            
            if batch_count >= 3:  # 只测试前3个批次
                break
        
        print(f"✅ 批处理测试成功，处理了 {batch_count} 个批次")
        return True
        
    except Exception as e:
        print(f"❌ 批处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_transforms():
    """测试数据变换"""
    print("\n🔍 测试数据变换...")
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        from ppmat.datasets.transforms import ComformerCompatTransform
        
        # 创建带变换的数据集
        dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_full",
            property_names=["e_form"],
            max_samples=10,
            overwrite=True,
            build_graph_cfg={
                "__class_name__": "ComformerGraphConverter",
                "__init_params__": {
                    "cutoff": 5.0,
                    "num_cpus": 1,
                    "atom_features": "cgcnn",
                    "max_neighbors": 25
                }
            },
            transforms=ComformerCompatTransform()
        )
        
        print(f"✅ 带变换的数据集创建成功")
        
        # 测试样本
        sample = dataset[0]
        print(f"  变换后样本键: {list(sample.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据变换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 MatBench数据集一致性测试")
    print("=" * 60)
    
    # 检查数据文件
    data_file = project_root / "data" / "matbench" / "matbench_mp_e_form_full.pkl"
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        print("请先运行: python test_matbench/download_matbench.py")
        return
    
    tests = [
        ("数据集加载", test_dataset_loading),
        ("样本一致性", lambda: test_sample_consistency(dataset)),
        ("批处理", lambda: test_batch_processing(dataset)),
        ("数据变换", test_data_transforms)
    ]
    
    dataset = None
    all_passed = True
    
    for i, (test_name, test_func) in enumerate(tests, 1):
        print(f"\n测试 {i}/4: {test_name}")
        print("-" * 40)
        
        if test_name == "数据集加载":
            dataset = test_func()
            if dataset is None:
                all_passed = False
                break
        else:
            if not test_func():
                all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过!")
        print("数据集已经过清洗和标准化，可以安全用于批处理训练")
    else:
        print("❌ 部分测试失败")
        print("请检查数据集实现或数据文件")
    
    print("\n📋 测试总结:")
    print("• 数据一致性: 确保所有样本有相同的键和数据类型")
    print("• 批处理兼容性: 验证可以正常进行批处理")
    print("• 数据清洗: 处理None/NaN值和异常数据")
    print("• 格式标准化: 统一数据格式避免collate_fn错误")

if __name__ == "__main__":
    main()
