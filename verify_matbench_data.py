#!/usr/bin/env python3
"""
验证MatBench数据集的完整性和格式
"""
import os
import pickle
import sys
from pathlib import Path

def verify_data_file(file_path):
    """验证数据文件的格式和内容"""
    
    if not os.path.exists(file_path):
        print(f"✗ 数据文件不存在: {file_path}")
        return False
    
    try:
        print(f"正在验证: {file_path}")
        
        # 加载数据
        with open(file_path, 'rb') as f:
            data = pickle.load(f)
        
        # 检查数据结构
        if not isinstance(data, dict):
            print("✗ 数据格式错误: 应该是字典格式")
            return False
        
        required_keys = ['graphs', 'props']
        for key in required_keys:
            if key not in data:
                print(f"✗ 缺少必要的键: {key}")
                return False
        
        graphs = data['graphs']
        props = data['props']
        
        # 检查graphs格式
        if not isinstance(graphs, dict):
            print("✗ graphs应该是字典格式")
            return False
        
        # 检查props格式
        if not isinstance(props, dict):
            print("✗ props应该是字典格式")
            return False
        
        if 'e_form' not in props:
            print("✗ props中缺少e_form属性")
            return False
        
        # 检查数据一致性
        num_graphs = len(graphs)
        num_props = len(props['e_form'])
        
        if num_graphs != num_props:
            print(f"✗ 数据不一致: graphs有{num_graphs}个，props有{num_props}个")
            return False
        
        print(f"✓ 数据验证通过")
        print(f"  样本数量: {num_graphs}")
        print(f"  图数据键范围: {min(graphs.keys())} ~ {max(graphs.keys())}")
        
        # 检查属性值统计
        e_form_values = props['e_form']
        if e_form_values:
            print(f"  形成能范围: {min(e_form_values):.3f} ~ {max(e_form_values):.3f} eV/atom")
            print(f"  平均形成能: {sum(e_form_values)/len(e_form_values):.3f} eV/atom")
        
        # 检查第一个样本的结构
        if 0 in graphs:
            first_structure = graphs[0]
            print(f"  第一个结构类型: {type(first_structure)}")
            
            # 如果是pymatgen Structure对象，显示更多信息
            try:
                if hasattr(first_structure, 'formula'):
                    print(f"  第一个结构化学式: {first_structure.formula}")
                if hasattr(first_structure, 'num_sites'):
                    print(f"  第一个结构原子数: {first_structure.num_sites}")
            except:
                pass
        
        return True
        
    except Exception as e:
        print(f"✗ 验证失败: {e}")
        return False

def check_training_readiness():
    """检查训练准备情况"""
    
    print("\n检查训练准备情况...")
    
    # 检查配置文件
    config_files = [
        "property_prediction/configs/comformer/comformer_matbench_mp_e_form_full.yaml",
        "property_prediction/configs/comformer/comformer_matbench_mp_e_form_500.yaml",
        "property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✓ 配置文件存在: {config_file}")
        else:
            print(f"✗ 配置文件缺失: {config_file}")
    
    # 检查训练脚本
    train_script = "property_prediction/train.py"
    if os.path.exists(train_script):
        print(f"✓ 训练脚本存在: {train_script}")
    else:
        print(f"✗ 训练脚本缺失: {train_script}")
    
    # 检查模型文件
    model_files = [
        "ppmat/models/comformer/comformer.py",
        "ppmat/datasets/transform/comformer_transform.py"
    ]
    
    for model_file in model_files:
        if os.path.exists(model_file):
            print(f"✓ 模型文件存在: {model_file}")
        else:
            print(f"✗ 模型文件缺失: {model_file}")

def estimate_training_time(num_samples):
    """估算训练时间"""
    
    # 基于经验的时间估算（单位：小时）
    time_estimates = {
        200: 0.5,      # 30分钟
        500: 1.0,      # 1小时
        1000: 2.0,     # 2小时
        5000: 8.0,     # 8小时
        10000: 16.0,   # 16小时
        50000: 48.0,   # 48小时
        100000: 72.0,  # 72小时
        132000: 96.0   # 96小时（完整数据集）
    }
    
    # 找到最接近的样本数量
    closest_samples = min(time_estimates.keys(), key=lambda x: abs(x - num_samples))
    estimated_hours = time_estimates[closest_samples] * (num_samples / closest_samples)
    
    if estimated_hours < 1:
        return f"{estimated_hours * 60:.0f} 分钟"
    elif estimated_hours < 24:
        return f"{estimated_hours:.1f} 小时"
    else:
        return f"{estimated_hours / 24:.1f} 天"

def main():
    """主函数"""
    print("MatBench数据集验证工具")
    print("=" * 50)
    
    # 要验证的数据文件列表
    data_files = [
        "./data/matbench/matbench_mp_e_form_full.pkl",
        "./data/matbench/matbench_mp_e_form_500.pkl", 
        "./data/matbench/matbench_mp_e_form_test_200.pkl",
        "./data/matbench/matbench_mp_e_form_processed.pkl"
    ]
    
    verified_files = []
    
    for data_file in data_files:
        if os.path.exists(data_file):
            if verify_data_file(data_file):
                verified_files.append(data_file)
                
                # 获取样本数量并估算训练时间
                with open(data_file, 'rb') as f:
                    data = pickle.load(f)
                    num_samples = len(data['graphs'])
                    estimated_time = estimate_training_time(num_samples)
                    print(f"  预计训练时间: {estimated_time}")
            print()
        else:
            print(f"跳过不存在的文件: {data_file}")
    
    # 检查训练准备情况
    check_training_readiness()
    
    print("\n" + "=" * 50)
    print("验证总结:")
    print(f"✓ 验证通过的数据文件: {len(verified_files)}")
    
    if verified_files:
        print("\n可用的数据集:")
        for i, file_path in enumerate(verified_files, 1):
            with open(file_path, 'rb') as f:
                data = pickle.load(f)
                num_samples = len(data['graphs'])
                file_name = os.path.basename(file_path)
                estimated_time = estimate_training_time(num_samples)
                print(f"  {i}. {file_name} ({num_samples:,} 样本, ~{estimated_time})")
        
        print("\n推荐使用顺序:")
        print("1. 先用小数据集测试: matbench_mp_e_form_test_200.pkl")
        print("2. 中等规模验证: matbench_mp_e_form_500.pkl") 
        print("3. 完整数据集训练: matbench_mp_e_form_full.pkl")
        
        print("\n训练命令示例:")
        print("python property_prediction/train.py -c property_prediction/configs/comformer/comformer_matbench_mp_e_form_full.yaml")
    else:
        print("✗ 没有找到有效的数据文件")
        print("请先运行 download_matbench_full.py 下载数据")

if __name__ == "__main__":
    main()
