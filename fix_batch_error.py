#!/usr/bin/env python3
"""
修复批处理错误的工具脚本
"""
import os
import shutil
import glob
from pathlib import Path

def clean_cache_files():
    """清理所有缓存文件"""
    print("🧹 清理缓存文件...")
    
    cache_patterns = [
        "./data/matbench/*_cache*",
        "./data/*_cache*", 
        "./__pycache__",
        "./ppmat/__pycache__",
        "./ppmat/**/__pycache__",
        "*.pyc"
    ]
    
    cleaned_count = 0
    
    for pattern in cache_patterns:
        files = glob.glob(pattern, recursive=True)
        for file_path in files:
            try:
                if os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                    print(f"✅ 删除目录: {file_path}")
                else:
                    os.remove(file_path)
                    print(f"✅ 删除文件: {file_path}")
                cleaned_count += 1
            except Exception as e:
                print(f"⚠️ 删除失败 {file_path}: {e}")
    
    print(f"✅ 清理完成，共清理 {cleaned_count} 个文件/目录")

def reduce_batch_size():
    """减小批次大小以避免内存问题"""
    print("\n🔧 调整配置文件批次大小...")
    
    config_files = [
        "property_prediction/configs/comformer/comformer_matbench_mp_e_form_1000.yaml",
        "property_prediction/configs/comformer/comformer_matbench_mp_e_form_500.yaml",
        "property_prediction/configs/comformer/comformer_matbench_mp_e_form_test.yaml"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 将批次大小从64减小到16
                content = content.replace('batch_size: 64', 'batch_size: 16')
                content = content.replace('batch_size: 32', 'batch_size: 16')
                
                # 减少worker数量
                content = content.replace('num_workers: 4', 'num_workers: 0')
                content = content.replace('num_workers: 6', 'num_workers: 0')
                content = content.replace('num_workers: 8', 'num_workers: 0')
                
                with open(config_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ 已调整: {config_file}")
                
            except Exception as e:
                print(f"⚠️ 调整失败 {config_file}: {e}")

def create_debug_config():
    """创建调试用的小批次配置"""
    print("\n🔍 创建调试配置...")
    
    debug_config = """Global:
  label_names:
  - e_form
  do_train: true
  do_eval: true
  do_test: true
  split_dataset_ratio:
    train: 0.8
    val: 0.1
    test: 0.1
  graph_converter:
    __class_name__: ComformerGraphConverter
    __init_params__:
      cutoff: 5.0
      num_cpus: 1
      atom_features: cgcnn
      max_neighbors: 25

Trainer:
  max_epochs: 10
  seed: 42
  output_dir: ./output/comformer_debug
  save_freq: 5
  log_freq: 1
  start_eval_epoch: 5
  eval_freq: 5
  pretrained_model_path: null
  resume_from_checkpoint: null
  use_amp: false
  amp_level: O1
  eval_with_no_grad: true
  gradient_accumulation_steps: 1
  best_metric_indicator: eval_metric
  name_for_best_metric: e_form
  greater_is_better: false
  compute_metric_during_train: true
  metric_strategy_during_eval: epoch

Model:
  __class_name__: iComformer
  __init_params__:
    conv_layers: 2
    edge_layers: 1
    atom_input_features: 92
    edge_features: 128
    triplet_input_features: 128
    node_features: 128
    fc_features: 128
    output_features: 1
    node_layer_head: 1
    edge_layer_head: 1
    property_name: ${Global.label_names}

Metric:
  e_form:
    __class_name__: paddle.nn.L1Loss
    __init_params__: {}

Optimizer:
  __class_name__: AdamW
  __init_params__:
    lr:
      __class_name__: OneCycleLR
      __init_params__:
        max_learning_rate: 0.001

Dataset:
  train:
    dataset:
      __class_name__: MatBenchDataset
      __init_params__:
        path: ./data/matbench
        task_name: matbench_mp_e_form_test_200
        property_name: e_form
        overwrite: true
        max_samples: 50
        transforms:
        - __class_name__: ComformerCompatTransform
          __init_params__: {}
    num_workers: 0
    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: true
        drop_last: false
        batch_size: 4
  val:
    dataset:
      __class_name__: MatBenchDataset
      __init_params__:
        path: ./data/matbench
        task_name: matbench_mp_e_form_test_200
        property_name: e_form
        overwrite: true
        max_samples: 50
        transforms:
        - __class_name__: ComformerCompatTransform
          __init_params__: {}
    num_workers: 0
    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: false
        drop_last: false
        batch_size: 4
  test:
    dataset:
      __class_name__: MatBenchDataset
      __init_params__:
        path: ./data/matbench
        task_name: matbench_mp_e_form_test_200
        property_name: e_form
        overwrite: true
        max_samples: 50
        transforms:
        - __class_name__: ComformerCompatTransform
          __init_params__: {}
    num_workers: 0
    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: false
        drop_last: false
        batch_size: 4

Predict:
  graph_converter: ${Global.graph_converter}
  eval_with_no_grad: true
"""
    
    debug_config_file = "property_prediction/configs/comformer/comformer_debug.yaml"
    
    try:
        os.makedirs(os.path.dirname(debug_config_file), exist_ok=True)
        with open(debug_config_file, 'w', encoding='utf-8') as f:
            f.write(debug_config)
        print(f"✅ 调试配置已创建: {debug_config_file}")
        return debug_config_file
    except Exception as e:
        print(f"❌ 创建调试配置失败: {e}")
        return None

def test_debug_training():
    """测试调试训练"""
    print("\n🧪 运行调试训练...")
    
    debug_config = create_debug_config()
    if not debug_config:
        return False
    
    import subprocess
    
    cmd = f"python property_prediction/train.py -c {debug_config}"
    print(f"执行命令: {cmd}")
    
    try:
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        if result.returncode == 0:
            print("✅ 调试训练成功!")
            print("最后几行输出:")
            print('\n'.join(result.stdout.split('\n')[-10:]))
            return True
        else:
            print("❌ 调试训练失败!")
            print("错误信息:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ 调试训练超时，但这可能是正常的")
        return True
    except Exception as e:
        print(f"❌ 调试训练异常: {e}")
        return False

def main():
    """主函数"""
    print("🔧 批处理错误修复工具")
    print("=" * 50)
    
    print("这个工具将:")
    print("1. 清理所有缓存文件")
    print("2. 调整配置文件批次大小")
    print("3. 创建调试配置")
    print("4. 运行调试训练测试")
    
    choice = input("\n是否继续? (y/n): ").lower().strip()
    if choice != 'y':
        print("取消执行")
        return
    
    # 步骤1: 清理缓存
    clean_cache_files()
    
    # 步骤2: 调整批次大小
    reduce_batch_size()
    
    # 步骤3: 测试调试训练
    if test_debug_training():
        print("\n✅ 修复成功!")
        print("现在可以尝试正常训练:")
        print("python property_prediction/train.py -c property_prediction/configs/comformer/comformer_matbench_mp_e_form_1000.yaml")
    else:
        print("\n❌ 仍有问题，请检查错误信息")
        print("建议:")
        print("1. 检查数据文件是否完整")
        print("2. 尝试重新下载数据")
        print("3. 检查环境依赖")

if __name__ == "__main__":
    main()
