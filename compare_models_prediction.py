#!/usr/bin/env python3
"""
对比不同模型的预测结果
"""
import os
import subprocess
import pandas as pd
from pathlib import Path

def run_prediction(model_config, description):
    """运行单个模型的预测"""
    
    print(f"\n{'='*60}")
    print(f"运行预测: {description}")
    print('='*60)
    
    try:
        if 'model_name' in model_config:
            # 预训练模型
            cmd = f"python property_prediction/predict.py --model_name='{model_config['model_name']}' --weights_name='{model_config['weights_name']}' --cif_file_path='{model_config['cif_path']}'"
        else:
            # 自训练模型
            cmd = f"python property_prediction/predict.py --config_path='{model_config['config_path']}' --weights_path='{model_config['weights_path']}' --cif_file_path='{model_config['cif_path']}'"
        
        print(f"命令: {cmd}")
        
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=True,
            text=True,
            timeout=300
        )
        
        if result.returncode == 0:
            print("✓ 预测成功")
            
            # 尝试解析输出中的预测结果
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if 'formation_energy_per_atom' in line:
                    print(f"预测结果: {line}")
                    # 提取数值
                    import re
                    match = re.search(r'formation_energy_per_atom.*?(-?\d+\.\d+)', line)
                    if match:
                        return float(match.group(1))
            
            # 如果没有在输出中找到，尝试读取result.csv
            if os.path.exists('result.csv'):
                df = pd.read_csv('result.csv')
                if 'formation_energy_per_atom' in df.columns:
                    return df['formation_energy_per_atom'].iloc[0]
            
            print("⚠️ 无法解析预测结果")
            return None
            
        else:
            print("✗ 预测失败")
            print(f"错误: {result.stderr}")
            return None
            
    except subprocess.TimeoutExpired:
        print("✗ 预测超时")
        return None
    except Exception as e:
        print(f"✗ 预测异常: {e}")
        return None

def main():
    """主函数"""
    print("模型预测结果对比")
    print("="*60)
    
    # 定义要对比的模型
    models = [
        {
            'name': 'MEGNet (MP2018)',
            'model_name': 'megnet_mp2018_train_60k_e_form',
            'weights_name': 'best.pdparams',
            'cif_path': './property_prediction/example_data/cifs/'
        }
    ]
    
    # 检查是否有训练好的Comformer模型
    comformer_models = []
    
    # 检查500样本模型
    comformer_500_path = Path('./output/comformer_matbench_mp_e_form_500_t_20250723_213450_s_42/checkpoints/best')
    if comformer_500_path.exists():
        comformer_models.append({
            'name': 'Comformer (500样本)',
            'config_path': 'property_prediction/configs/comformer/comformer_matbench_mp_e_form_500.yaml',
            'weights_path': str(comformer_500_path),
            'cif_path': './property_prediction/example_data/cifs/'
        })

    # 检查1000样本模型
    output_dir = Path('./output')
    if output_dir.exists():
        for item in output_dir.iterdir():
            if item.name.startswith('comformer_matbench_mp_e_form_1000'):
                best_path = item / 'checkpoints' / 'best'
                if best_path.exists():
                    comformer_models.append({
                        'name': 'Comformer (1000样本)',
                        'config_path': 'property_prediction/configs/comformer/comformer_matbench_mp_e_form_1000.yaml',
                        'weights_path': str(best_path),
                        'cif_path': './property_prediction/example_data/cifs/'
                    })
                    break
    
    # 检查完整数据集模型
    output_dir = Path('./output')
    if output_dir.exists():
        for item in output_dir.iterdir():
            if item.name.startswith('comformer_matbench_mp_e_form_full'):
                best_path = item / 'checkpoints' / 'best'
                if best_path.exists():
                    comformer_models.append({
                        'name': 'Comformer (完整数据集)',
                        'config_path': 'property_prediction/configs/comformer/comformer_matbench_mp_e_form_full.yaml',
                        'weights_path': str(best_path),
                        'cif_path': './property_prediction/example_data/cifs/'
                    })
                    break
    
    # 合并所有模型
    all_models = models + comformer_models
    
    if len(all_models) == 1:
        print("只找到MEGNet预训练模型，建议先训练Comformer模型进行对比")
    
    # 运行预测
    results = {}
    
    for model in all_models:
        result = run_prediction(model, model['name'])
        results[model['name']] = result
    
    # 显示对比结果
    print(f"\n{'='*60}")
    print("预测结果对比")
    print('='*60)
    
    if results:
        print(f"{'模型名称':<25} {'形成能 (eV/atom)':<20} {'稳定性':<10}")
        print('-' * 60)
        
        for model_name, prediction in results.items():
            if prediction is not None:
                stability = "稳定" if prediction < 0 else "不稳定"
                print(f"{model_name:<25} {prediction:<20.6f} {stability:<10}")
            else:
                print(f"{model_name:<25} {'预测失败':<20} {'-':<10}")
        
        # 计算差异
        valid_results = {k: v for k, v in results.items() if v is not None}
        if len(valid_results) > 1:
            print(f"\n模型间差异分析:")
            values = list(valid_results.values())
            max_diff = max(values) - min(values)
            print(f"最大差异: {max_diff:.6f} eV/atom")
            
            # 找出最稳定和最不稳定的预测
            min_energy = min(valid_results.items(), key=lambda x: x[1])
            max_energy = max(valid_results.items(), key=lambda x: x[1])
            print(f"最稳定预测: {min_energy[0]} ({min_energy[1]:.6f} eV/atom)")
            print(f"最不稳定预测: {max_energy[0]} ({max_energy[1]:.6f} eV/atom)")
    
    else:
        print("没有成功的预测结果")
    
    print(f"\n{'='*60}")
    print("建议:")
    if len(comformer_models) == 0:
        print("1. 训练Comformer模型以进行更全面的对比")
        print("2. 使用完整MatBench数据集训练以获得更好的性能")
    else:
        print("1. 对比不同模型的预测结果，分析差异原因")
        print("2. 在更多样本上测试模型的泛化能力")
        print("3. 考虑集成多个模型的预测结果")

if __name__ == "__main__":
    main()
