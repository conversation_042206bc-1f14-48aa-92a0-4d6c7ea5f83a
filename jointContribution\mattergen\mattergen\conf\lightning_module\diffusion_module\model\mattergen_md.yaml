_target_: mattergen.denoiser.GemNetTDenoiser
hidden_dim: 512
gemnet:
  _target_: mattergen.common.gemnet.gemnet_md.GemNetT_MD
  num_targets: 1
  latent_dim: ${eval:'${..hidden_dim} * (1 + len(${..property_embeddings}))'}  # 1 is for time encoding.
  atom_embedding:
    _target_: mattergen.common.gemnet.layers.embedding_block.AtomEmbedding
    emb_size: ${...hidden_dim}
    with_mask_type: ${eval:'${...denoise_atom_types} and "${...atom_type_diffusion}" == "mask"'}
  emb_size_atom: ${..hidden_dim}
  emb_size_edge: ${..hidden_dim}
  max_neighbors: 50
  max_cell_images_per_dim: 5
  cutoff: 7.
  num_blocks: 4
  regress_stress: true
  otf_graph: true
  scale_file: ${oc.env:PROJECT_ROOT}/common/gemnet/gemnet-dT.json
denoise_atom_types: true
atom_type_diffusion: mask
property_embeddings_adapt: {}
property_embeddings: {}
defaults: [] # NOTE: to train a conditional model, unccoment entries such as property_embeddings@property_embeddings.chemical_system: chemical_system below and edit/add properties to the defaults list as desired.
  # see https://stackoverflow.com/questions/71356361/selecting-multiple-configs-from-a-config-group-in-hydra-without-using-an-explici
  # add via config override: +lightning_module/diffusion_module/model/property_embeddings@lightning_module.diffusion_module.model.property_embeddings.dft_bulk_modulus=dft_bulk_modulus
  # delete via config override: ~lightning_module/diffusion_module/model/property_embeddings@lightning_module.diffusion_module.model.property_embeddings.chemical_system
  # - property_embeddings@property_embeddings.chemical_system: chemical_system
  # - property_embeddings@property_embeddings.dft_bulk_modulus: dft_bulk_modulus
