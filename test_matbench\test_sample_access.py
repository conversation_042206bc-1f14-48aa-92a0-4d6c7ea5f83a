#!/usr/bin/env python3
"""
测试样本获取模块
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_sample_access():
    """测试样本获取功能"""
    print("🔍 测试样本获取模块...")
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        
        # 创建完整的数据集（不构建图，简化测试）
        dataset = MatBenchDataset(
            path="./data/matbench/matbench_mp_e_form_full.pkl",
            property_names=["e_form"],
            build_structure_cfg={
                "format": "pymatgen_structure",
                "num_cpus": 1
            },
            build_graph_cfg=None,  # 不构建图
            max_samples=3,
            overwrite=True
        )
        
        print(f"✅ 数据集创建成功")
        print(f"  样本数: {len(dataset)}")
        
        # 测试获取单个样本
        sample = dataset[0]
        
        print(f"✅ 样本获取成功")
        print(f"  样本键: {list(sample.keys())}")
        print(f"  graph类型: {type(sample.get('graph'))}")
        print(f"  e_form类型: {type(sample.get('e_form'))}")
        print(f"  e_form值: {sample.get('e_form')}")
        print(f"  id值: {sample.get('id')}")
        
        # 验证样本格式
        required_keys = {'graph', 'e_form', 'id'}
        assert set(sample.keys()) == required_keys, f"样本键不匹配，期望{required_keys}，实际{set(sample.keys())}"
        assert sample['e_form'] is not None, "e_form为空"
        assert sample['id'] == 0, "id不正确"
        
        # 测试多个样本的一致性
        samples = [dataset[i] for i in range(min(3, len(dataset)))]
        first_keys = set(samples[0].keys())
        
        for i, sample in enumerate(samples):
            assert set(sample.keys()) == first_keys, f"样本{i}的键不一致"
            assert sample['id'] == i, f"样本{i}的id不正确"
        
        print("✅ 样本格式验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 样本获取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 样本获取模块测试")
    print("=" * 30)
    
    # 检查数据文件
    data_file = project_root / "data" / "matbench" / "matbench_mp_e_form_full.pkl"
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    if test_sample_access():
        print("\n🎉 样本获取模块测试通过!")
    else:
        print("\n❌ 样本获取模块测试失败")

if __name__ == "__main__":
    main()
