# Copyright (c) 2023 PaddlePaddle Authors. All Rights Reserved.

# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at

#     http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from __future__ import annotations

import numpy as np
import paddle

__all__ = [
    "ComformerCompatTransform",
]


class ComformerCompatTransform:
    """
    Transform to ensure data compatibility with Comformer model.

    This transform ensures that the graph data format is compatible with the
    Comformer model requirements, specifically:
    - node_feat["node_feat"] for node features
    - edge_feat["r"] for edge distance vectors
    - edge_feat["nei"] for neighbor information
    """

    def __init__(self):
        """Initialize the ComformerCompatTransform."""
        pass

    def __call__(self, data):
        """
        Apply the transform to the data.

        Args:
            data (dict): Input data dictionary containing graph and properties

        Returns:
            dict: Transformed data dictionary
        """
        # Make a copy to avoid modifying the original data
        transformed_data = {}

        for key, value in data.items():
            if key == 'graph':
                # Transform graph data for Comformer compatibility
                transformed_data[key] = self._transform_graph(value)
            elif isinstance(value, np.ndarray):
                # Convert numpy arrays to paddle tensors
                transformed_data[key] = paddle.to_tensor(value)
            else:
                # Keep other data as is
                transformed_data[key] = value

        return transformed_data

    def _transform_graph(self, graph):
        """
        Transform graph data to ensure Comformer compatibility.

        Args:
            graph: Graph data (PGL Graph)

        Returns:
            Transformed graph data with proper node_feat and edge_feat structure
        """
        # If it's not a PGL graph, return as is
        if not (hasattr(graph, 'num_nodes') and hasattr(graph, 'num_edges')):
            return graph

        # Check if the graph already has the required structure
        if (hasattr(graph, 'node_feat') and isinstance(graph.node_feat, dict) and
            'node_feat' in graph.node_feat):
            # Graph already has the correct structure
            return graph

        # Transform the graph to have the required structure
        try:
            # Work directly with the graph (no deep copy to avoid issues)

            # Ensure node_feat has 'node_feat' key
            if hasattr(graph, 'node_feat') and isinstance(graph.node_feat, dict):
                if 'node_feat' not in graph.node_feat:
                    # Try to use atom_types as node features
                    if 'atom_types' in graph.node_feat:
                        atom_types = graph.node_feat['atom_types']
                        # Convert to paddle tensor if it's numpy array
                        if isinstance(atom_types, np.ndarray):
                            atom_types = paddle.to_tensor(atom_types, dtype='int64')
                        else:
                            atom_types = atom_types.cast('int64')

                        # Convert to one-hot encoding (92 is the max atomic number)
                        max_atomic_num = 92
                        atom_types_clamped = paddle.clip(atom_types, min=1, max=max_atomic_num) - 1  # 0-indexed
                        one_hot = paddle.nn.functional.one_hot(atom_types_clamped, num_classes=max_atomic_num)

                        graph.node_feat['node_feat'] = one_hot.cast('float32')
                    else:
                        # Create default node features
                        num_nodes = graph.num_nodes
                        graph.node_feat['node_feat'] = paddle.ones([num_nodes, 1], dtype='float32')

            # Ensure edge_feat has 'r' and 'nei' keys
            if hasattr(graph, 'edge_feat') and isinstance(graph.edge_feat, dict):
                if 'r' not in graph.edge_feat:
                    # Try to use bond_vec as r if available
                    if 'bond_vec' in graph.edge_feat:
                        bond_vec = graph.edge_feat['bond_vec']
                        # Convert to paddle tensor if it's numpy array
                        if isinstance(bond_vec, np.ndarray):
                            bond_vec = paddle.to_tensor(bond_vec, dtype='float32')
                        else:
                            bond_vec = bond_vec.cast('float32')
                        graph.edge_feat['r'] = bond_vec
                    else:
                        # Create default edge features
                        num_edges = graph.num_edges
                        graph.edge_feat['r'] = paddle.ones([num_edges, 3], dtype='float32')

                if 'nei' not in graph.edge_feat:
                    # 'nei' should be lattice vectors for each edge, shape [num_edges, 3, 3]
                    # If we have lattice information, use it; otherwise create identity matrices
                    num_edges = graph.num_edges

                    if hasattr(graph, 'node_feat') and 'lattice' in graph.node_feat:
                        # Use the lattice from node features
                        lattice = graph.node_feat['lattice']
                        if isinstance(lattice, np.ndarray):
                            lattice = paddle.to_tensor(lattice, dtype='float32')
                        else:
                            lattice = lattice.cast('float32')

                        # Expand lattice to [num_edges, 3, 3]
                        if lattice.shape == (1, 3, 3):
                            lattice = lattice.squeeze(0)  # [3, 3]
                        if lattice.shape == (3, 3):
                            # Repeat for each edge
                            graph.edge_feat['nei'] = lattice.unsqueeze(0).tile(repeat_times=[num_edges, 1, 1])
                        else:
                            # Create default identity matrices
                            graph.edge_feat['nei'] = paddle.eye(3, dtype='float32').unsqueeze(0).tile(repeat_times=[num_edges, 1, 1])
                    else:
                        # Create default identity matrices for each edge
                        graph.edge_feat['nei'] = paddle.eye(3, dtype='float32').unsqueeze(0).tile(repeat_times=[num_edges, 1, 1])

            return graph

        except Exception as e:
            print(f"Warning: Failed to transform graph structure: {e}")
            import traceback
            traceback.print_exc()
            return graph
    
    def __repr__(self):
        return f"{self.__class__.__name__}()"
