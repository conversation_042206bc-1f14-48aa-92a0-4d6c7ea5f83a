model_path: ${oc.env:MAP_INPUT_DIR}
load_epoch: latest
full_finetuning: true

adapter:
  # these arguments are used to initialize GemNetTAdapter
  # more args are added by the finetuning script during runtime
  _target_: mattergen.adapter.GemNetTAdapter
  property_embeddings_adapt: {}

defaults: []
  # path/to/<EMAIL>: config_file_name
  ## e.g., insert values from dft_bulk_modulus.yaml in /lightning_module/diffusion_module/model/property_embeddings/
  ## into adapter.property_embeddings_adapt[dft_bulk_modulus]
  # - /lightning_module/diffusion_module/model/property_embeddings@adapter.property_embeddings_adapt.dft_bulk_modulus: dft_bulk_modulus
