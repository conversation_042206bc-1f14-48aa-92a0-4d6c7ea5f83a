# _target_: pytorch_lightning.Trainer
# accelerator: 'gpu'
# devices: 1
# num_nodes: 1
# precision: 32
# max_epochs: ${data_module.max_epochs}
# accumulate_grad_batches: 1
# gradient_clip_val: 0.5
# gradient_clip_algorithm: value
# check_val_every_n_epoch: 5
# strategy:
#   _target_: pytorch_lightning.strategies.ddp.DDPStrategy
#   find_unused_parameters: true
  
# logger:
#   _target_: pytorch_lightning.loggers.WandbLogger
#   project: crystal-generation
#   job_type: train
#   settings:
#     _target_: wandb.Settings
#     start_method: fork
#     _save_requirements: False

# callbacks:
#   - _target_: pytorch_lightning.callbacks.LearningRateMonitor
#     logging_interval: step
#     log_momentum: False
#   - _target_: pytorch_lightning.callbacks.ModelCheckpoint
#     monitor: loss_val
#     mode: min
#     save_top_k: 1
#     save_last: True
#     verbose: false
#     every_n_epochs: 1
#     filename: "{epoch}-{loss_val:.2f}"
#   - _target_: pytorch_lightning.callbacks.TQDMProgressBar
#     refresh_rate: 50
#   - _target_: mattergen.common.data.callback.SetPropertyScalers

output_dir: 'output'
save_freq: 10
log_freq: 10
start_eval_epoch: 1
eval_freq: 1
seed: 42
pretrained_model_path: null 
checkpoint_path: null
scale_grad: true
is_save_traj: false
step_lr: 0.000005
mode: 'train'
max_epochs: ${data_module.max_epochs}
