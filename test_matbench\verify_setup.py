#!/usr/bin/env python3
"""
验证MatBench设置的简单脚本
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def verify_environment():
    """验证环境"""
    print("🔍 验证环境...")
    
    # 检查必要的包
    required_packages = ['paddle', 'pymatgen', 'matbench']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n需要安装: {' '.join(missing_packages)}")
        return False
    
    return True

def verify_dataset_class():
    """验证数据集类"""
    print("\n🔍 验证MatBenchDataset类...")
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        print("✅ MatBenchDataset类导入成功")
        
        # 检查类的关键方法
        required_methods = ['__init__', '__len__', '__getitem__']
        for method in required_methods:
            if hasattr(MatBenchDataset, method):
                print(f"✅ 方法 {method} 存在")
            else:
                print(f"❌ 方法 {method} 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ MatBenchDataset类导入失败: {e}")
        return False

def verify_data_file():
    """验证数据文件"""
    print("\n🔍 验证数据文件...")
    
    data_file = project_root / "data" / "matbench" / "matbench_mp_e_form_full.pkl"
    
    if data_file.exists():
        print(f"✅ 数据文件存在: {data_file}")
        
        # 检查文件大小
        file_size = data_file.stat().st_size / (1024 * 1024)  # MB
        print(f"  文件大小: {file_size:.1f} MB")
        
        if file_size > 10:  # 期望至少10MB
            print("✅ 文件大小合理")
            return True
        else:
            print("⚠️ 文件大小可能不正确")
            return False
    else:
        print(f"❌ 数据文件不存在: {data_file}")
        print("请运行: python test_matbench/download_matbench.py")
        return False

def test_small_dataset():
    """测试小规模数据集加载"""
    print("\n🔍 测试小规模数据集加载...")
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        
        # 测试加载10个样本
        dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_full",
            property_name="e_form",
            max_samples=10,
            overwrite=True
        )
        
        print(f"✅ 小规模数据集加载成功!")
        print(f"  样本数: {len(dataset)}")
        print(f"  期望样本数: 10")
        
        if len(dataset) == 10:
            print("✅ 样本数量正确")
        else:
            print(f"⚠️ 样本数量不匹配: 期望10，实际{len(dataset)}")
        
        # 测试获取样本
        sample = dataset[0]
        print(f"  样本键: {list(sample.keys())}")
        
        # 检查必要的键
        required_keys = ['graph', 'e_form']
        for key in required_keys:
            if key in sample:
                print(f"✅ 样本包含键: {key}")
            else:
                print(f"❌ 样本缺少键: {key}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 小规模数据集测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_config_file():
    """验证配置文件"""
    print("\n🔍 验证配置文件...")
    
    config_file = Path(__file__).parent / "test_config_1000.yaml"
    
    if config_file.exists():
        print(f"✅ 配置文件存在: {config_file}")
        
        # 简单检查配置内容
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            required_sections = ['Global', 'Trainer', 'Model', 'Dataset']
            for section in required_sections:
                if section in content:
                    print(f"✅ 配置包含: {section}")
                else:
                    print(f"❌ 配置缺少: {section}")
                    return False
            
            # 检查MatBenchDataset配置
            if 'MatBenchDataset' in content:
                print("✅ 配置使用MatBenchDataset")
            else:
                print("❌ 配置未使用MatBenchDataset")
                return False
            
            # 检查max_samples配置
            if 'max_samples: 1000' in content:
                print("✅ 配置包含max_samples限制")
            else:
                print("⚠️ 配置可能缺少max_samples限制")
            
            return True
            
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")
            return False
    else:
        print(f"❌ 配置文件不存在: {config_file}")
        return False

def main():
    """主函数"""
    print("🎯 MatBench设置验证")
    print("=" * 50)
    
    checks = [
        ("环境检查", verify_environment),
        ("数据集类检查", verify_dataset_class),
        ("数据文件检查", verify_data_file),
        ("小规模测试", test_small_dataset),
        ("配置文件检查", verify_config_file)
    ]
    
    all_passed = True
    
    for i, (check_name, check_func) in enumerate(checks, 1):
        print(f"\n检查 {i}/5: {check_name}")
        print("-" * 30)
        
        if not check_func():
            print(f"❌ {check_name} 失败")
            all_passed = False
        else:
            print(f"✅ {check_name} 通过")
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有检查通过!")
        print("现在可以运行完整测试:")
        print("  python test_matbench/test_matbench_1000.py")
    else:
        print("❌ 部分检查失败")
        print("请根据上述提示修复问题后重试")
    
    print("\n📋 文件结构:")
    print("test_matbench/")
    print("├── download_matbench.py     # 下载MatBench数据")
    print("├── verify_setup.py          # 验证设置（当前脚本）")
    print("├── test_config_1000.yaml    # 1000样本测试配置")
    print("└── test_matbench_1000.py    # 完整测试脚本")

if __name__ == "__main__":
    main()
