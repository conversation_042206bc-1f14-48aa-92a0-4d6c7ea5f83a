#!/usr/bin/env python3
"""
简化的MatBench数据集测试
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_basic_loading():
    """测试基础加载功能"""
    print("🔍 测试基础数据加载...")
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        
        # 最简单的配置，不使用图转换
        dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_full",
            property_names=["e_form"],
            max_samples=10,  # 只测试10个样本
            overwrite=True,
            build_graph_cfg=None  # 不构建图，避免复杂的转换
        )
        
        print(f"✅ 基础数据集加载成功!")
        print(f"  样本数: {len(dataset)}")
        print(f"  属性名: {dataset.property_names}")
        
        # 测试获取样本
        sample = dataset[0]
        print(f"  第一个样本键: {list(sample.keys())}")
        print(f"  第一个样本e_form: {sample.get('e_form', 'N/A')}")
        
        return dataset
        
    except Exception as e:
        print(f"❌ 基础数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_with_graph_conversion():
    """测试带图转换的加载"""
    print("\n🔍 测试带图转换的数据加载...")
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        
        # 带图转换的配置
        dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_full",
            property_names=["e_form"],
            max_samples=5,  # 只测试5个样本
            overwrite=True,
            build_graph_cfg={
                "__class_name__": "ComformerGraphConverter",
                "__init_params__": {
                    "cutoff": 5.0,
                    "num_cpus": 1,
                    "atom_features": "cgcnn",
                    "max_neighbors": 25
                }
            }
        )
        
        print(f"✅ 带图转换的数据集加载成功!")
        print(f"  样本数: {len(dataset)}")
        
        # 测试获取样本
        sample = dataset[0]
        print(f"  第一个样本键: {list(sample.keys())}")
        
        # 检查图数据
        if 'graph' in sample:
            graph = sample['graph']
            if hasattr(graph, 'num_nodes'):
                print(f"  图节点数: {graph.num_nodes}")
                print(f"  图边数: {graph.num_edges}")
            else:
                print(f"  图类型: {type(graph)}")
        
        return dataset
        
    except Exception as e:
        print(f"❌ 带图转换的数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_batch_processing_simple(dataset):
    """简单的批处理测试"""
    print("\n🔍 测试简单批处理...")
    
    if dataset is None:
        print("❌ 数据集为空，跳过测试")
        return False
    
    try:
        # 手动创建批次，避免复杂的DataLoader
        batch_size = 3
        batch_samples = []
        
        for i in range(min(batch_size, len(dataset))):
            sample = dataset[i]
            batch_samples.append(sample)
        
        print(f"  创建了 {len(batch_samples)} 个样本的批次")
        
        # 检查批次一致性
        if batch_samples:
            first_keys = set(batch_samples[0].keys())
            all_consistent = all(set(sample.keys()) == first_keys for sample in batch_samples)
            
            if all_consistent:
                print("✅ 批次中所有样本的键一致")
                
                # 检查数据类型
                for key in first_keys:
                    types = [type(sample[key]) for sample in batch_samples]
                    if len(set(str(t) for t in types)) == 1:
                        print(f"  ✅ 键 '{key}' 的数据类型一致: {types[0]}")
                    else:
                        print(f"  ❌ 键 '{key}' 的数据类型不一致: {types}")
                        return False
                
                return True
            else:
                print("❌ 批次中样本的键不一致")
                for i, sample in enumerate(batch_samples):
                    print(f"    样本 {i}: {set(sample.keys())}")
                return False
        
        return False
        
    except Exception as e:
        print(f"❌ 简单批处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_cleaning():
    """测试数据清洗功能"""
    print("\n🔍 测试数据清洗功能...")
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        
        # 创建数据集实例来测试清洗方法
        dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_full",
            property_names=["e_form"],
            max_samples=5,
            overwrite=True,
            build_graph_cfg=None
        )
        
        # 测试数据清洗方法
        test_data = [1.5, None, float('nan'), "2.5", -1000000, 3.7]
        cleaned_data = dataset._clean_property_data(test_data, "test_prop")
        
        print(f"  原始数据: {test_data}")
        print(f"  清洗后数据: {cleaned_data}")
        
        # 验证清洗结果
        expected_types = [float] * len(cleaned_data)
        actual_types = [type(x) for x in cleaned_data]
        
        if actual_types == expected_types:
            print("✅ 数据清洗功能正常，所有值都是float类型")
            return True
        else:
            print(f"❌ 数据清洗失败，类型不一致: {actual_types}")
            return False
        
    except Exception as e:
        print(f"❌ 数据清洗测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 MatBench数据集简化测试")
    print("=" * 50)
    
    # 检查数据文件
    data_file = project_root / "data" / "matbench" / "matbench_mp_e_form_full.pkl"
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        print("请先运行: python test_matbench/download_matbench.py")
        return
    
    print(f"✅ 数据文件存在: {data_file}")
    
    tests = [
        ("基础数据加载", test_basic_loading),
        ("数据清洗功能", test_data_cleaning),
        ("带图转换加载", test_with_graph_conversion),
    ]
    
    dataset = None
    all_passed = True
    
    for i, (test_name, test_func) in enumerate(tests, 1):
        print(f"\n测试 {i}/{len(tests)}: {test_name}")
        print("-" * 30)
        
        if test_name == "基础数据加载":
            dataset = test_func()
            if dataset is None:
                all_passed = False
                break
        elif test_name == "带图转换加载":
            graph_dataset = test_func()
            if graph_dataset is not None:
                # 测试图数据集的批处理
                print("\n  测试图数据集批处理...")
                if not test_batch_processing_simple(graph_dataset):
                    all_passed = False
        else:
            if not test_func():
                all_passed = False
    
    # 测试基础数据集的批处理
    if dataset is not None:
        print(f"\n测试 {len(tests)+1}: 基础数据集批处理")
        print("-" * 30)
        if not test_batch_processing_simple(dataset):
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过!")
        print("MatBench数据集可以正常使用")
        print("\n下一步:")
        print("• 运行完整训练: python test_matbench/test_matbench_1000.py")
        print("• 或使用配置文件: python property_prediction/train.py -c test_matbench/test_config_1000.yaml")
    else:
        print("❌ 部分测试失败")
        print("请检查错误信息并修复问题")
    
    print("\n📋 测试说明:")
    print("• 基础加载: 测试不带图转换的数据加载")
    print("• 数据清洗: 验证数据标准化功能")
    print("• 图转换: 测试完整的图构建流程")
    print("• 批处理: 验证数据一致性，避免collate_fn错误")

if __name__ == "__main__":
    main()
