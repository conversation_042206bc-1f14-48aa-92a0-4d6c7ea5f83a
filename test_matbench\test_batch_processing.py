#!/usr/bin/env python3
"""
测试批处理模块
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_batch_processing():
    """测试批处理功能"""
    print("🔍 测试批处理模块...")
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        from paddle.io import DataLoader
        from ppmat.datasets.collate_fn import DefaultCollator
        
        # 创建数据集
        dataset = MatBenchDataset(
            path="./data/matbench/matbench_mp_e_form_full.pkl",
            property_names=["e_form"],
            build_structure_cfg={
                "format": "pymatgen_structure",
                "num_cpus": 1
            },
            build_graph_cfg=None,  # 不构建图，简化测试
            max_samples=8,
            overwrite=True
        )
        
        print(f"✅ 数据集创建成功，样本数: {len(dataset)}")
        
        # 创建数据加载器
        dataloader = DataLoader(
            dataset,
            batch_size=4,
            shuffle=False,
            num_workers=0,
            collate_fn=DefaultCollator(),
            drop_last=False
        )
        
        print(f"✅ 数据加载器创建成功")
        
        # 测试批处理
        batch_count = 0
        for batch in dataloader:
            batch_count += 1
            print(f"  批次 {batch_count}:")
            print(f"    键: {list(batch.keys())}")
            
            for key, value in batch.items():
                if hasattr(value, 'shape'):
                    print(f"    {key}: 形状={value.shape}, 类型={type(value)}")
                elif isinstance(value, list):
                    print(f"    {key}: 列表长度={len(value)}, 类型={type(value)}")
                else:
                    print(f"    {key}: 类型={type(value)}")
            
            # 验证批次格式
            assert 'graph' in batch, "批次缺少graph键"
            assert 'e_form' in batch, "批次缺少e_form键"
            assert 'id' in batch, "批次缺少id键"
            
            if batch_count >= 2:  # 只测试前2个批次
                break
        
        print(f"✅ 批处理验证通过，处理了 {batch_count} 个批次")
        return True
        
    except Exception as e:
        print(f"❌ 批处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 批处理模块测试")
    print("=" * 30)
    
    # 检查数据文件
    data_file = project_root / "data" / "matbench" / "matbench_mp_e_form_full.pkl"
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    if test_batch_processing():
        print("\n🎉 批处理模块测试通过!")
    else:
        print("\n❌ 批处理模块测试失败")

if __name__ == "__main__":
    main()
