#!/usr/bin/env python3
"""
测试属性数据处理模块
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_property_data():
    """测试属性数据处理功能"""
    print("🔍 测试属性数据处理模块...")
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        
        # 创建数据集实例
        dataset = MatBenchDataset.__new__(MatBenchDataset)
        dataset.path = "./data/matbench/matbench_mp_e_form_full.pkl"
        dataset.max_samples = 5
        dataset.num_samples = 5
        
        # 先读取数据
        data, num_samples = dataset.read_data(dataset.path)
        
        # 测试read_property_data方法
        property_names = ["e_form"]
        property_data = dataset.read_property_data(data, property_names)
        
        print(f"✅ 属性数据处理成功")
        print(f"  属性名: {list(property_data.keys())}")
        print(f"  e_form数量: {len(property_data['e_form'])}")
        print(f"  e_form类型: {type(property_data['e_form'][0])}")
        print(f"  e_form示例: {property_data['e_form'][:3]}")
        
        # 验证属性数据
        assert 'e_form' in property_data, "缺少e_form属性"
        assert len(property_data['e_form']) == num_samples, "属性数量不匹配"
        assert all(isinstance(x, (int, float)) for x in property_data['e_form']), "属性类型不正确"
        
        print("✅ 属性数据验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 属性数据处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 属性数据处理模块测试")
    print("=" * 30)
    
    # 检查数据文件
    data_file = project_root / "data" / "matbench" / "matbench_mp_e_form_full.pkl"
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    if test_property_data():
        print("\n🎉 属性数据处理模块测试通过!")
    else:
        print("\n❌ 属性数据处理模块测试失败")

if __name__ == "__main__":
    main()
