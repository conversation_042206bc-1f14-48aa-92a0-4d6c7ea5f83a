#!/usr/bin/env python3
"""
测试数据读取模块
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_data_reading():
    """测试MatBench数据读取功能"""
    print("🔍 测试数据读取模块...")
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        
        # 创建数据集实例，只测试数据读取
        dataset = MatBenchDataset.__new__(MatBenchDataset)
        dataset.path = "./data/matbench/matbench_mp_e_form_full.pkl"
        dataset.max_samples = 5
        
        # 测试read_data方法
        data, num_samples = dataset.read_data(dataset.path)
        
        print(f"✅ 数据读取成功")
        print(f"  样本数: {num_samples}")
        print(f"  数据键: {list(data.keys())}")
        print(f"  结构类型: {type(data['structure'][0])}")
        
        # 验证数据格式
        assert 'structure' in data, "缺少structure键"
        assert 'e_form' in data, "缺少e_form键"
        assert len(data['structure']) == num_samples, "结构数量不匹配"
        assert len(data['e_form']) == num_samples, "属性数量不匹配"
        
        print("✅ 数据格式验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据读取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 数据读取模块测试")
    print("=" * 30)
    
    # 检查数据文件
    data_file = project_root / "data" / "matbench" / "matbench_mp_e_form_full.pkl"
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    if test_data_reading():
        print("\n🎉 数据读取模块测试通过!")
    else:
        print("\n❌ 数据读取模块测试失败")

if __name__ == "__main__":
    main()
