# Copyright (c) 2023 PaddlePaddle Authors. All Rights Reserved.

# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at

#     http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from __future__ import absolute_import
from __future__ import annotations

import math
import os
import os.path as osp
import pickle
from collections import defaultdict
from typing import Any
from typing import Callable
from typing import Dict
from typing import Optional

import numpy as np
import paddle.distributed as dist
from paddle.io import Dataset

from ppmat.datasets.build_structure import BuildStructure
from ppmat.datasets.custom_data_type import ConcatData
from ppmat.models import build_graph_converter
from ppmat.utils import logger
from ppmat.utils.misc import is_equal


class MatBenchDataset(Dataset):
    """MatBench Dataset Handler - 模仿MP2018Dataset风格

    这个类提供了加载和处理MatBench材料科学数据集的工具。
    数据集包含来自Materials Project数据库的无机材料计算属性。

    **数据集概述**
    - **来源**: MatBench基准数据集
    - **任务**: 形成能预测 (matbench_mp_e_form)
    - **格式**: 包含结构和属性的Pickle文件

    **数据格式**
    数据集以pickle格式存储，结构如下：

    ```python
    {
        "graphs": {
            0: <pymatgen.Structure>,    # 晶体结构
            1: <pymatgen.Structure>,
            # ...
        },
        "props": {
            "e_form": [                 # 形成能 (eV/atom)
                -1.8169,
                -1.8948,
                # ...
            ]
        }
    }
    ```

    Args:
        path (str): 数据集目录路径
        task_name (str): MatBench任务名称 (如 'matbench_mp_e_form_full')
        property_names (Optional[list[str]]): 要使用的属性名称列表
        build_structure_cfg (Dict, optional): 构建pymatgen结构的配置
        build_graph_cfg (Dict, optional): 从结构构建图的配置
        transforms (Optional[Callable], optional): 每个样本的预处理变换
        cache_path (Optional[str], optional): 处理数据的缓存路径
        overwrite (bool, optional): 覆盖现有缓存
        filter_unvalid (bool, optional): 是否过滤无效样本
        max_samples (Optional[int], optional): 最大样本数量。如果为None，加载所有样本
    """

    def __init__(
        self,
        path: str = "./data/matbench",
        task_name: str = "matbench_mp_e_form_full",
        property_names: Optional[list[str]] = None,
        build_structure_cfg: Dict = None,
        build_graph_cfg: Dict = None,
        transforms: Optional[Callable] = None,
        cache_path: Optional[str] = None,
        overwrite: bool = False,
        filter_unvalid: bool = True,
        max_samples: Optional[int] = None,  # 保留用于测试
        **kwargs,  # for compatibility
    ):
        super().__init__()

        # Handle path - can be directory or direct file path
        if path.endswith('.pkl'):
            # Direct file path
            data_file = path
        else:
            # Directory path, construct file path
            data_file = osp.join(path, f"{task_name}.pkl")

        if not osp.exists(data_file):
            raise FileNotFoundError(
                f"MatBench data file not found: {data_file}. "
                f"Please download MatBench dataset first."
            )

        self.path = data_file
        self.task_name = task_name
        self.max_samples = max_samples

        if isinstance(property_names, str):
            property_names = [property_names]

        if build_structure_cfg is None:
            build_structure_cfg = {
                "format": "pymatgen_structure",
                "primitive": False,
                "niggli": True,
                "num_cpus": 1,
            }
            logger.message(
                "build_structure_cfg未设置，使用默认配置: "
                f"{build_structure_cfg}"
            )

        self.property_names = property_names if property_names is not None else []
        self.build_structure_cfg = build_structure_cfg
        self.build_graph_cfg = build_graph_cfg
        self.transforms = transforms

        if cache_path is not None:
            self.cache_path = cache_path
        else:
            # 类似MP2018Dataset的缓存路径生成
            cache_suffix = f"_{max_samples}" if max_samples else ""
            self.cache_path = osp.join(
                osp.split(data_file)[0] + "_cache", 
                osp.splitext(osp.basename(data_file))[0] + cache_suffix
            )
        logger.info(f"缓存路径: {self.cache_path}")

        self.overwrite = overwrite
        self.filter_unvalid = filter_unvalid

        self.cache_exists = True if osp.exists(self.cache_path) else False
        
        # 读取原始数据 - 类似MP2018Dataset的流程
        self.row_data, self.num_samples = self.read_data(data_file)
        logger.info(f"从 {data_file} 加载 {self.num_samples} 个样本")
        
        # 读取属性数据
        self.property_data = self.read_property_data(self.row_data, self.property_names)

        # 缓存处理逻辑 - 类似MP2018Dataset
        if self.cache_exists and not overwrite:
            logger.warning(
                "缓存已启用。如果缓存文件存在，将自动读取并忽略当前设置。"
                "请确保缓存时使用的设置与当前设置匹配。"
            )
            try:
                build_structure_cfg_cache = self.load_from_cache(
                    osp.join(self.cache_path, "build_structure_cfg.pkl")
                )
                if is_equal(build_structure_cfg_cache, build_structure_cfg):
                    logger.info(
                        "缓存的build_structure_cfg配置与当前设置匹配。"
                        "重用之前生成的结构数据以优化性能。"
                    )
                else:
                    logger.warning(
                        "build_structure_cfg与缓存不同。将重新构建结构和图。"
                    )
                    overwrite = True
            except Exception as e:
                logger.warning(f"从缓存加载build_structure_cfg失败: {e}")
                overwrite = True

            if build_graph_cfg is not None and not overwrite:
                try:
                    build_graph_cfg_cache = self.load_from_cache(
                        osp.join(self.cache_path, "build_graph_cfg.pkl")
                    )
                    if is_equal(build_graph_cfg_cache, build_graph_cfg):
                        logger.info("缓存的build_graph_cfg配置匹配。重用缓存数据。")
                    else:
                        logger.warning("build_graph_cfg与缓存不同。将重新构建图。")
                        overwrite = True
                except Exception as e:
                    logger.warning(f"从缓存加载build_graph_cfg失败: {e}")
                    overwrite = True

        # 构建结构和图 - 类似MP2018Dataset
        self.build_structure = BuildStructure(**build_structure_cfg)
        self.structures = self.build_structures(overwrite)

        if build_graph_cfg is not None:
            self.graph_converter = build_graph_converter(build_graph_cfg)
            self.graphs = self.build_graphs(overwrite)
        else:
            self.graph_converter = None
            self.graphs = None

        # 保存缓存配置
        if not self.cache_exists or overwrite:
            self.save_to_cache(
                build_structure_cfg, osp.join(self.cache_path, "build_structure_cfg.pkl")
            )
            if build_graph_cfg is not None:
                self.save_to_cache(
                    build_graph_cfg, osp.join(self.cache_path, "build_graph_cfg.pkl")
                )

        # 过滤无效样本
        if filter_unvalid:
            self.filter_unvalid_by_property()

    def read_data(self, path: str):
        """Read MatBench data - following MP2018Dataset style

        Args:
            path (str): Path to the MatBench pickle file
        """
        with open(path, 'rb') as f:
            raw_data = pickle.load(f)

        # Validate data format
        if not isinstance(raw_data, dict) or 'graphs' not in raw_data or 'props' not in raw_data:
            raise ValueError(
                f"Invalid MatBench data format. Expected dict with 'graphs' and 'props' keys."
            )

        graphs_data = raw_data['graphs']
        props_data = raw_data['props']

        # Convert to consistent format like MP2018Dataset
        if isinstance(graphs_data, dict):
            # Convert dict indices to list
            indices = sorted(graphs_data.keys(), key=lambda x: int(x))
            num_samples = len(indices)

            # Create data dict similar to MP2018Dataset format
            data = defaultdict(list)
            data['structure'] = [graphs_data[idx] for idx in indices]

            # Add property data
            for prop_key, prop_values in props_data.items():
                if isinstance(prop_values, dict):
                    data[prop_key] = [prop_values[idx] for idx in indices]
                else:
                    data[prop_key] = list(prop_values)[:num_samples]
        else:
            raise ValueError("Unsupported MatBench data format")

        # Apply max_samples limit for testing
        if self.max_samples is not None:
            num_samples = min(num_samples, self.max_samples)
            for key in data:
                data[key] = data[key][:num_samples]
            logger.info(f"Limited to {self.max_samples} samples")

        return data, num_samples

    def read_property_data(self, data: Dict, property_names: list[str]):
        """Read property data - following MP2018Dataset style

        Args:
            data (Dict): Data that contains the property data
            property_names (list[str]): Property names
        """
        property_data = {}
        for property_name in property_names:
            if property_name not in data:
                raise ValueError(f"{property_name} not found in the data")
            property_data[property_name] = [
                data[property_name][i] for i in range(self.num_samples)
            ]
        return property_data



    def filter_unvalid_by_property(self):
        """Filter invalid samples - following MP2018Dataset style"""
        for property_name in self.property_names:
            data = self.property_data[property_name]
            reserve_idx = []
            for i, data_item in enumerate(data):
                if isinstance(data_item, str) or (data_item is not None and not math.isnan(data_item)):
                    reserve_idx.append(i)

            # Update all data
            for key in self.property_data.keys():
                self.property_data[key] = [
                    self.property_data[key][i] for i in reserve_idx
                ]

            self.row_data = [self.row_data[i] for i in reserve_idx]
            self.structures = [self.structures[i] for i in reserve_idx]
            if self.graphs is not None:
                self.graphs = [self.graphs[i] for i in reserve_idx]

            logger.warning(
                f"Filter out {len(reserve_idx)} samples with valid properties: "
                f"{property_name}"
            )

        self.num_samples = len(self.row_data)
        logger.warning(f"Remaining {self.num_samples} samples after filtering.")

    # 以下方法从MP2018Dataset复制，保持一致的接口
    def build_structures(self, overwrite: bool = False):
        """构建结构 - 修复分布式代码问题"""
        structure_cache_path = osp.join(self.cache_path, "structures")
        if osp.exists(structure_cache_path) and not overwrite:
            logger.info("发现结构缓存，从缓存加载")
            structures = [
                osp.join(structure_cache_path, f"{i:010d}.pkl")
                for i in range(self.num_samples)
            ]
        else:
            logger.info("构建结构")
            os.makedirs(structure_cache_path, exist_ok=True)

            # 检查是否在分布式环境中
            should_process = True
            try:
                # 只有rank 0进程进行转换（如果在分布式环境中）
                if dist.is_initialized():
                    should_process = (dist.get_rank() == 0)
            except:
                # 如果不在分布式环境中，直接处理
                should_process = True

            if should_process:
                # 保存结构到缓存
                for i, structure in enumerate(self.row_data):
                    # MatBench数据已经是pymatgen结构，直接保存
                    structure_file = osp.join(structure_cache_path, f"{i:010d}.pkl")
                    with open(structure_file, 'wb') as f:
                        pickle.dump(structure, f)

                    if (i + 1) % 1000 == 0:
                        logger.info(f"已处理 {i + 1}/{self.num_samples} 个结构")

            # 等待所有进程（仅在分布式环境中）
            try:
                if dist.is_initialized():
                    dist.barrier()
            except:
                pass  # 忽略分布式相关错误

            structures = [
                osp.join(structure_cache_path, f"{i:010d}.pkl")
                for i in range(self.num_samples)
            ]

        return structures

    def build_graphs(self, overwrite: bool = False):
        """构建图 - 修复分布式代码问题"""
        graph_cache_path = osp.join(self.cache_path, "graphs")
        if osp.exists(graph_cache_path) and not overwrite:
            logger.info("发现图缓存，从缓存加载")
            graphs = [
                osp.join(graph_cache_path, f"{i:010d}.pkl")
                for i in range(self.num_samples)
            ]
        else:
            logger.info("构建图")
            os.makedirs(graph_cache_path, exist_ok=True)

            # 检查是否在分布式环境中
            should_process = True
            try:
                # 只有rank 0进程进行转换（如果在分布式环境中）
                if dist.is_initialized():
                    should_process = (dist.get_rank() == 0)
            except:
                # 如果不在分布式环境中，直接处理
                should_process = True

            if should_process:
                successful_graphs = []
                failed_count = 0

                for i, structure_path in enumerate(self.structures):
                    try:
                        # 加载结构
                        with open(structure_path, 'rb') as f:
                            structure = pickle.load(f)

                        # 验证结构
                        if not self._validate_structure(structure):
                            logger.warning(f"结构 {i} 验证失败，跳过")
                            failed_count += 1
                            continue

                        # 转换为图
                        graph = self.graph_converter(structure)

                        # 验证图
                        if not self._validate_graph(graph):
                            logger.warning(f"图 {i} 验证失败，跳过")
                            failed_count += 1
                            continue

                        # 保存图到缓存
                        graph_file = osp.join(graph_cache_path, f"{i:010d}.pkl")
                        with open(graph_file, 'wb') as f:
                            pickle.dump(graph, f)

                        successful_graphs.append(graph_file)

                        if (i + 1) % 100 == 0:
                            logger.info(f"已处理 {i + 1}/{self.num_samples} 个图，失败 {failed_count} 个")

                    except Exception as e:
                        logger.warning(f"处理图 {i} 时出错: {e}")
                        failed_count += 1
                        continue

                if failed_count > 0:
                    logger.warning(f"总共 {failed_count} 个图处理失败")

            # 等待所有进程（仅在分布式环境中）
            try:
                if dist.is_initialized():
                    dist.barrier()
            except:
                pass  # 忽略分布式相关错误

            graphs = [
                osp.join(graph_cache_path, f"{i:010d}.pkl")
                for i in range(self.num_samples)
            ]

        return graphs

    def _validate_structure(self, structure):
        """验证结构数据的有效性"""
        try:
            # 检查是否是pymatgen结构
            if hasattr(structure, 'lattice') and hasattr(structure, 'species'):
                # 检查原子数量
                if len(structure) == 0:
                    return False
                # 检查晶格参数
                if any(param <= 0 for param in structure.lattice.abc):
                    return False
                return True
            return False
        except Exception:
            return False

    def _validate_graph(self, graph):
        """验证图数据的有效性 - 针对PGL图优化"""
        try:
            # 检查是否是PGL图
            if str(type(graph)) == "<class 'pgl.graph.Graph'>":
                # PGL图，检查基本属性
                if hasattr(graph, 'num_nodes') and graph.num_nodes > 0:
                    return True
                return False
            # 检查是否有图的基本属性
            elif hasattr(graph, 'num_nodes'):
                # 检查节点数量
                if graph.num_nodes <= 0:
                    return False
                return True
            # 如果不是PGL图，但是有其他图属性，也认为有效
            elif hasattr(graph, 'nodes') or hasattr(graph, 'edges'):
                return True
            # 如果是其他类型的图对象，也认为有效
            elif str(type(graph)).find('graph') != -1 or str(type(graph)).find('Graph') != -1:
                return True
            return False
        except Exception as e:
            # 如果验证过程中出现异常，记录但不阻止
            logger.debug(f"图验证异常: {e}")
            return True  # 默认认为有效，让后续流程处理

    def load_from_cache(self, cache_path: str):
        """从缓存加载 - 从MP2018Dataset复制"""
        with open(cache_path, 'rb') as f:
            return pickle.load(f)

    def save_to_cache(self, data: Any, cache_path: str):
        """保存到缓存 - 从MP2018Dataset复制"""
        os.makedirs(osp.dirname(cache_path), exist_ok=True)
        with open(cache_path, 'wb') as f:
            pickle.dump(data, f)

    def __len__(self):
        """返回数据集大小"""
        return self.num_samples

    def __getitem__(self, idx):
        """Get single sample - following MP2018Dataset style"""
        data = {}

        if self.graphs is not None:
            graph = self.graphs[idx]
            if isinstance(graph, str):
                graph = self.load_from_cache(graph)
            data["graph"] = graph
        else:
            structure = self.structures[idx]
            if isinstance(structure, str):
                structure = self.load_from_cache(structure)
            data["graph"] = structure

        for property_name in self.property_names:
            if property_name in self.property_data:
                data[property_name] = np.array(
                    [self.property_data[property_name][idx]]
                ).astype("float32")
            else:
                raise KeyError(f"Property {property_name} not found.")

        data["id"] = idx
        data = self.transforms(data) if self.transforms is not None else data

        return data


