#!/usr/bin/env python3
"""
基础测试 - 只测试核心功能
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_dataset_basic():
    """测试数据集基础功能"""
    print("🔍 测试MatBench数据集基础功能...")

    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset

        # 创建小规模数据集
        dataset = MatBenchDataset(
            path="./data/matbench/matbench_mp_e_form_full.pkl",
            property_names=["e_form"],
            build_structure_cfg={
                "format": "pymatgen_structure",
                "num_cpus": 1
            },
            build_graph_cfg=None,  # 不构建图，简化测试
            max_samples=10,
            overwrite=True
        )

        print(f"✅ 数据集创建成功")
        print(f"  样本数: {len(dataset)}")
        print(f"  属性名: {dataset.property_names}")

        # 测试获取样本
        sample = dataset[0]
        print(f"  样本键: {list(sample.keys())}")
        print(f"  e_form类型: {type(sample['e_form'])}")
        print(f"  e_form值: {sample['e_form']}")

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_graph():
    """测试带图转换的数据集"""
    print("\n🔍 测试带图转换的数据集...")

    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset

        # 创建带图转换的小规模数据集
        dataset = MatBenchDataset(
            path="./data/matbench/matbench_mp_e_form_full.pkl",
            property_names=["e_form"],
            build_structure_cfg={
                "format": "pymatgen_structure",
                "num_cpus": 1
            },
            build_graph_cfg={
                "__class_name__": "ComformerGraphConverter",
                "__init_params__": {
                    "cutoff": 5.0,
                    "num_cpus": 1,
                    "atom_features": "cgcnn",
                    "max_neighbors": 25
                }
            },
            max_samples=5,  # 只测试5个样本
            overwrite=True
        )

        print(f"✅ 带图数据集创建成功")
        print(f"  样本数: {len(dataset)}")

        # 测试获取样本
        sample = dataset[0]
        print(f"  样本键: {list(sample.keys())}")

        graph = sample.get('graph')
        if graph is not None:
            print(f"  图类型: {type(graph)}")
            if hasattr(graph, 'num_nodes'):
                print(f"  图节点数: {graph.num_nodes}")

        return True

    except Exception as e:
        print(f"❌ 带图测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 MatBench基础测试")
    print("=" * 30)

    # 检查数据文件
    data_file = project_root / "data" / "matbench" / "matbench_mp_e_form_full.pkl"
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return

    tests = [
        ("基础功能", test_dataset_basic),
        ("图转换", test_with_graph),
    ]

    all_passed = True
    for test_name, test_func in tests:
        if not test_func():
            all_passed = False
            break

    if all_passed:
        print("\n🎉 所有测试通过!")
        print("现在可以尝试训练:")
        print("  python property_prediction/train.py -c test_matbench/test_config_1000.yaml")
    else:
        print("\n❌ 部分测试失败")

if __name__ == "__main__":
    main()
