#!/usr/bin/env python3
"""
MatBench 1000样本数据集一键设置和训练脚本
"""
import os
import subprocess
import time
from pathlib import Path

def check_environment():
    """检查环境和依赖"""
    print("🔍 检查环境...")
    
    # 检查必要的包
    required_packages = ['matbench', 'pymatgen', 'paddle']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n需要安装: {' '.join(missing_packages)}")
        print("运行: pip install matbench pymatgen")
        return False
    
    return True

def download_and_prepare_data():
    """下载并准备1000样本数据"""
    print("\n📥 下载MatBench完整数据集并创建1000样本子集...")
    
    # 检查是否已存在1000样本数据
    data_file = "./data/matbench/matbench_mp_e_form_1000.pkl"
    if os.path.exists(data_file):
        print(f"✅ 1000样本数据已存在: {data_file}")
        return True
    
    # 运行下载脚本
    try:
        print("开始下载，这可能需要10-20分钟...")
        result = subprocess.run(
            "python download_matbench_full.py",
            shell=True,
            capture_output=True,
            text=True,
            timeout=1800  # 30分钟超时
        )
        
        if result.returncode == 0:
            print("✅ 数据下载和准备完成!")
            print(result.stdout)
            return True
        else:
            print("❌ 数据下载失败!")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 下载超时，请检查网络连接")
        return False
    except Exception as e:
        print(f"❌ 下载异常: {e}")
        return False

def verify_data():
    """验证数据完整性"""
    print("\n🔍 验证数据完整性...")
    
    try:
        result = subprocess.run(
            "python verify_matbench_data.py",
            shell=True,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.returncode == 0:
            print("✅ 数据验证通过!")
            # 提取关键信息
            lines = result.stdout.split('\n')
            for line in lines:
                if '1000' in line and ('样本' in line or 'samples' in line):
                    print(f"  {line}")
            return True
        else:
            print("⚠️ 数据验证有警告，但可以继续")
            return True
            
    except Exception as e:
        print(f"⚠️ 验证异常: {e}")
        return True  # 不阻止继续执行

def start_training():
    """开始训练1000样本模型"""
    print("\n🚀 开始训练1000样本Comformer模型...")
    
    config_file = "property_prediction/configs/comformer/comformer_matbench_mp_e_form_1000.yaml"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    cmd = f"python property_prediction/train.py -c {config_file}"
    print(f"执行命令: {cmd}")
    print("预计训练时间: 2-3小时")
    print("预期最终MAE: 0.08-0.12 eV/atom")
    print("=" * 60)
    
    # 询问是否后台运行
    choice = input("是否在后台运行训练? (y/n): ").lower().strip()
    
    if choice == 'y':
        # 后台运行
        log_file = "training_1000_samples.log"
        cmd_bg = f"nohup {cmd} > {log_file} 2>&1 &"
        
        try:
            subprocess.Popen(cmd_bg, shell=True)
            print(f"✅ 训练已在后台启动")
            print(f"日志文件: {log_file}")
            print(f"监控命令: tail -f {log_file}")
            return True
        except Exception as e:
            print(f"❌ 后台启动失败: {e}")
            return False
    else:
        # 前台运行
        try:
            start_time = time.time()
            
            # 实时显示输出
            process = subprocess.Popen(
                cmd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 实时打印输出
            for line in process.stdout:
                print(line.rstrip())
            
            process.wait()
            
            end_time = time.time()
            duration = end_time - start_time
            
            if process.returncode == 0:
                print(f"\n✅ 训练完成! 用时: {duration/3600:.1f} 小时")
                return True
            else:
                print(f"\n❌ 训练失败! 返回码: {process.returncode}")
                return False
                
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断训练")
            return False
        except Exception as e:
            print(f"\n❌ 训练异常: {e}")
            return False

def show_results():
    """显示训练结果"""
    print("\n📊 查看训练结果...")
    
    # 查找输出目录
    output_pattern = "./output/comformer_matbench_mp_e_form_1000_*"
    import glob
    output_dirs = glob.glob(output_pattern)
    
    if not output_dirs:
        print("❌ 未找到训练输出目录")
        return False
    
    # 使用最新的输出目录
    latest_output = max(output_dirs, key=os.path.getctime)
    print(f"📁 输出目录: {latest_output}")
    
    # 检查日志文件
    log_file = os.path.join(latest_output, "run.log")
    if os.path.exists(log_file):
        print(f"📄 日志文件: {log_file}")
        
        # 提取最终结果
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 查找最终评估结果
            final_results = []
            for line in reversed(lines[-100:]):  # 检查最后100行
                if "Eval:" in line and "e_form" in line:
                    final_results.append(line.strip())
                    if len(final_results) >= 3:
                        break
            
            if final_results:
                print("\n📈 最终训练结果:")
                for result in reversed(final_results):
                    print(f"  {result}")
            
            # 查找最佳结果
            best_results = []
            for line in lines:
                if "best" in line.lower() and "e_form" in line:
                    best_results.append(line.strip())
            
            if best_results:
                print("\n🏆 最佳结果:")
                for result in best_results[-3:]:  # 显示最后3个最佳结果
                    print(f"  {result}")
                    
        except Exception as e:
            print(f"⚠️ 读取日志失败: {e}")
    
    # 检查模型文件
    checkpoints_dir = os.path.join(latest_output, "checkpoints")
    if os.path.exists(checkpoints_dir):
        print(f"\n💾 模型检查点: {checkpoints_dir}")
        
        # 列出可用的检查点
        checkpoints = []
        for item in os.listdir(checkpoints_dir):
            checkpoint_path = os.path.join(checkpoints_dir, item)
            if os.path.isdir(checkpoint_path):
                checkpoints.append(item)
        
        if checkpoints:
            print("  可用检查点:")
            for cp in sorted(checkpoints):
                print(f"    - {cp}")
    
    return True

def run_prediction_test():
    """运行预测测试"""
    print("\n🔮 测试预测功能...")
    
    # 查找最新的模型
    output_pattern = "./output/comformer_matbench_mp_e_form_1000_*"
    import glob
    output_dirs = glob.glob(output_pattern)
    
    if not output_dirs:
        print("❌ 未找到训练好的模型")
        return False
    
    latest_output = max(output_dirs, key=os.path.getctime)
    best_model_path = os.path.join(latest_output, "checkpoints", "best")
    
    if not os.path.exists(best_model_path):
        print(f"❌ 最佳模型不存在: {best_model_path}")
        return False
    
    # 运行预测
    config_path = "./property_prediction/configs/comformer/comformer_matbench_mp_e_form_1000.yaml"
    cif_path = "./property_prediction/example_data/cifs/"
    
    cmd = f"python property_prediction/predict.py --config_path='{config_path}' --weights_path='{best_model_path}' --cif_file_path='{cif_path}'"
    
    try:
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=True,
            text=True,
            timeout=120
        )
        
        if result.returncode == 0:
            print("✅ 预测测试成功!")
            print("预测结果:")
            print(result.stdout)
            return True
        else:
            print("❌ 预测测试失败!")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 预测测试异常: {e}")
        return False

def main():
    """主函数 - 完整流程"""
    print("🎯 MatBench 1000样本数据集完整流程")
    print("=" * 60)
    
    steps = [
        ("检查环境", check_environment),
        ("下载数据", download_and_prepare_data),
        ("验证数据", verify_data),
        ("开始训练", start_training),
        ("查看结果", show_results),
        ("预测测试", run_prediction_test)
    ]
    
    for i, (step_name, step_func) in enumerate(steps, 1):
        print(f"\n步骤 {i}/6: {step_name}")
        print("-" * 40)
        
        if not step_func():
            print(f"❌ 步骤 {i} 失败")
            
            if i <= 3:  # 前3步失败则停止
                print("关键步骤失败，停止执行")
                return False
            else:  # 后续步骤失败可以继续
                choice = input("是否继续下一步? (y/n): ").lower().strip()
                if choice != 'y':
                    break
        
        if i < len(steps) and i != 4:  # 训练步骤不需要额外暂停
            input("\n按回车键继续下一步...")
    
    print("\n🎉 流程完成!")
    print("=" * 60)
    print("📋 总结:")
    print("• 已下载MatBench完整数据集")
    print("• 已创建1000样本高质量子集")
    print("• 已完成Comformer模型训练")
    print("• 预期性能: MAE 0.08-0.12 eV/atom")
    print("• 相比200样本有显著提升")
    
    print("\n🚀 下一步建议:")
    print("• 对比不同规模数据集的性能")
    print("• 在更多CIF文件上测试预测")
    print("• 考虑使用完整132k数据集进一步提升")

if __name__ == "__main__":
    main()
