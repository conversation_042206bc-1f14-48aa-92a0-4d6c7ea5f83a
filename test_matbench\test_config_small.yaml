# 小规模测试配置 - 验证修复效果
Global:
  seed: 42
  epochs: 2  # 只训练2个epoch
  save_interval: 1
  eval_interval: 1
  output_dir: ./test_matbench/output/small_test
  label_names: ["e_form"]
  graph_converter:
    __class_name__: ComformerGraphConverter
    __init_params__:
      cutoff: 5.0
      num_cpus: 1
      atom_features: cgcnn
      max_neighbors: 25

Trainer:
  __class_name__: BaseTrainer
  __init_params__:
    use_tensorboard: false

Model:
  __class_name__: iComformer
  __init_params__:
    conv_layers: 2  # 减少层数
    edge_layers: 1
    atom_input_features: 92
    edge_features: 128  # 减少特征数
    triplet_input_features: 128
    node_features: 128
    fc_features: 128
    output_features: 1
    node_layer_head: 2
    edge_layer_head: 2
    property_name: ${Global.label_names}

Metric:
  e_form:
    __class_name__: paddle.nn.L1Loss
    __init_params__: {}

Optimizer:
  __class_name__: AdamW
  __init_params__:
    lr:
      __class_name__: OneCycleLR
      __init_params__:
        max_learning_rate: 0.001
    weight_decay: 1e-5

Dataset:
  train:
    dataset:
      __class_name__: MatBenchDataset
      __init_params__:
        path: ./data/matbench
        task_name: matbench_mp_e_form_full
        property_name: e_form
        overwrite: false
        max_samples: 50  # 训练集：前50个样本
        skip_samples: 0
        build_graph_cfg: ${Global.graph_converter}
        transforms:
        - __class_name__: ComformerCompatTransform
          __init_params__: {}
    num_workers: 0
    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: true
        drop_last: true
        batch_size: 8  # 小批次
  val:
    dataset:
      __class_name__: MatBenchDataset
      __init_params__:
        path: ./data/matbench
        task_name: matbench_mp_e_form_full
        property_name: e_form
        overwrite: false
        max_samples: 70  # 验证集：第51-70个样本
        skip_samples: 50
        build_graph_cfg: ${Global.graph_converter}
        transforms:
        - __class_name__: ComformerCompatTransform
          __init_params__: {}
    num_workers: 0
    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: false
        drop_last: false
        batch_size: 8
  test:
    dataset:
      __class_name__: MatBenchDataset
      __init_params__:
        path: ./data/matbench
        task_name: matbench_mp_e_form_full
        property_name: e_form
        overwrite: false
        max_samples: 80  # 测试集：第71-80个样本
        skip_samples: 70
        build_graph_cfg: ${Global.graph_converter}
        transforms:
        - __class_name__: ComformerCompatTransform
          __init_params__: {}
    num_workers: 0
    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: false
        drop_last: false
        batch_size: 8

Predict:
  graph_converter: ${Global.graph_converter}
  eval_with_no_grad: true
