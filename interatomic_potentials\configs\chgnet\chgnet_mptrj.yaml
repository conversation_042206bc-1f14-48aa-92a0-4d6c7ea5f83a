Global:
  do_train: True
  do_eval: False
  do_test: False

  label_names: ['energy_per_atom', 'force', 'stress', 'magmom']
  graph_converter:
    __class_name__: CHGNetGraphConverter
    __init_params__:
      cutoff: 5.0
      pdc: [1, 1, 1]
      num_classes: 95
      atom_graph_cutoff: 6.0
      bond_graph_cutoff: 3.0

  prim_eager_enabled: True
  prim_backward_white_list: ['concat_grad', 'gather_grad', 'layer_norm_grad', 'split_grad']


Trainer:
  # Max epochs to train
  max_epochs: 20
  # Random seed
  seed: 42
  # Save path for checkpoints and logs
  output_dir: ./output/chgnet_mptrj
  # Save frequency [epoch], for example, save_freq=10 means save checkpoints every 10 epochs
  save_freq: 100 # set 0 to disable saving during training
  # Logging frequency [step], for example, log_freq=10 means log every 10 steps
  log_freq: 20 # log frequency [step]

  # Start evaluation epoch, for example, start_eval_epoch=10 means start evaluation from epoch 10
  start_eval_epoch: 1
  # Evaluation frequency [epoch], for example, eval_freq=1 means evaluate every 1 epoch
  eval_freq: 1 # set 0 to disable evaluation during training
  # Pretrained model path, if null, no pretrained model will be loaded
  pretrained_model_path: null
  # Pretrained weight name, will be used when pretrained_model_path is a directory
  pretrained_weight_name: null #'latest.pdparams'
  # Resume from checkpoint path, useful for resuming training
  resume_from_checkpoint: null
  # whether use automatic mixed precision
  use_amp: False
  # automatic mixed precision level
  amp_level: 'O1'
  # whether run a model on no_grad mode during evaluation, useful for saving memory
  # If the model contains higher-order derivatives in the forward, it should be set to
  # False
  eval_with_no_grad: False
  # gradient accumulation steps, for example, gradient_accumulation_steps=2 means
  # gradient accumulation every 2 forward steps
  # Note:
  # one complete step  = gradient_accumulation_steps * forward steps + backward steps
  gradient_accumulation_steps: 1

  # best metric indicator, you can choose from "train_loss", "eval_loss", "train_metric", "eval_metric"
  best_metric_indicator: 'eval_metric' # "train_loss", "eval_loss", "train_metric", "eval_metric"
  # The name of the best metric, since you may have multiple metrics, such as "mae", "rmse", "mape"
  name_for_best_metric: "energy_per_atom"
  # The metric whether is better when it is greater
  greater_is_better: False

  # compute metric during training or evaluation
  compute_metric_during_train: True # True: the metric will be calculated on train dataset
  metric_strategy_during_eval: 'epoch' # step or epoch, compute metric after step or epoch, if set to 'step', the metric will be calculated after every step, else after epoch

  # whether use visualdl, wandb, tensorboard to log
  use_visualdl: False
  use_wandb: False
  use_tensorboard: False


Model:
  __class_name__: CHGNet
  __init_params__:
    atom_fea_dim: 64
    bond_fea_dim: 64
    angle_fea_dim: 64
    composition_model: "MPtrj"
    num_radial: 31
    num_angular: 31
    n_conv: 4
    atom_conv_hidden_dim: 64
    update_bond: True
    bond_conv_hidden_dim: 64
    update_angle: True
    angle_layer_hidden_dim: 0
    conv_dropout:  0
    read_out: "ave"
    mlp_hidden_dims: [64, 64, 64]
    mlp_dropout:  0
    mlp_first: True
    is_intensive: True
    atom_graph_cutoff:  6
    bond_graph_cutoff:  3
    cutoff_coeff: 8
    learnable_rbf: True
    is_freeze: False
    property_names: ['energy_per_atom', 'force', 'stress', 'magmom']
    return_site_energies: False
    return_atom_feas: False
    return_crystal_feas: False


Optimizer:
  __class_name__: Adam
  __init_params__:
    lr:
      __class_name__: Cosine
      __init_params__:
        learning_rate: 1e-3
        eta_min: 1e-5
        by_epoch: False


Metric:
  energy_per_atom:
    __class_name__: IgnoreNanMetricWrapper #MAEMetric
    __init_params__:
      __class_name__: paddle.nn.L1Loss
      __init_params__: {}
  force:
    __class_name__: IgnoreNanMetricWrapper #MAEMetric
    __init_params__:
      __class_name__: paddle.nn.L1Loss
      __init_params__: {}
  stress:
    __class_name__: IgnoreNanMetricWrapper #MAEMetric
    __init_params__:
      __class_name__: paddle.nn.L1Loss
      __init_params__: {}
  magmom:
    __class_name__: IgnoreNanMetricWrapper #MAEMetric
    __init_params__:
      __class_name__: paddle.nn.L1Loss
      __init_params__: {}

Dataset:
  train:
    dataset:
      __class_name__: MPTrjDataset
      __init_params__:
        path: "./data/MPtrj_2022.9_full/train.json"
        property_names: ${Global.label_names}
        build_structure_cfg:
          format: dict
          num_cpus: 10
        build_graph_cfg: ${Global.graph_converter}
        filter_unvalid: False
        transforms:
          - __class_name__: Scale
            __init_params__:
              scale: -0.1
              apply_keys: ['stress']
          - __class_name__: Abs
            __init_params__:
              apply_keys: ['magmom']
      num_workers: 0
      use_shared_memory: False

    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: True
        drop_last: True
        batch_size: 40
  val:
    dataset:
      __class_name__: MPTrjDataset
      __init_params__:
        path: "./data/MPtrj_2022.9_full/val.json"
        property_names: ${Global.label_names}
        build_structure_cfg:
          format: dict
          num_cpus: 10
        build_graph_cfg: ${Global.graph_converter}
        filter_unvalid: False
        transforms:
          - __class_name__: Scale
            __init_params__:
              scale: -0.1
              apply_keys: ['stress']
          - __class_name__: Abs
            __init_params__:
              apply_keys: ['magmom']
      num_workers: 0
      use_shared_memory: False
    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: False
        drop_last: False
        batch_size: 16
  test:
    dataset:
      __class_name__: MPTrjDataset
      __init_params__:
        path: "./data/MPtrj_2022.9_full/test.json"
        property_names: ${Global.label_names}
        build_structure_cfg:
          format: dict
          num_cpus: 10
        build_graph_cfg: ${Global.graph_converter}
        filter_unvalid: False
        transforms:
          - __class_name__: Scale
            __init_params__:
              scale: -0.1
              apply_keys: ['stress']
          - __class_name__: Abs
            __init_params__:
              apply_keys: ['magmom']
      num_workers: 0
      use_shared_memory: False
    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: False
        drop_last: False
        batch_size: 16


Predict:
  graph_converter: ${Global.graph_converter}
  eval_with_no_grad: False
