# Copyright (c) 2023 PaddlePaddle Authors. All Rights Reserved.

# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at

#     http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


from __future__ import annotations

import numbers
from collections.abc import Mapping
from collections.abc import Sequence
from typing import Any
from typing import List

import numpy as np
import paddle
import pgl

from ppmat.datasets.custom_data_type import ConcatData
from ppmat.datasets.custom_data_type import ConcatNumpyWarper


class DefaultCollator:
    """
    Default collate function for batching data with robust error handling.
    """

    def __call__(self, batch: List[Any]) -> Any:
        sample = batch[0]
        if isinstance(sample, ConcatNumpyWarper):
            batch = np.concatenate(batch, axis=0)
            return batch
        elif isinstance(sample, np.ndarray):
            try:
                batch = np.stack(batch, axis=0)
                return batch
            except:
                batch = np.array(batch)
                return batch
        elif isinstance(sample, (paddle.Tensor, paddle.framework.core.eager.Tensor)):
            return paddle.stack(batch, axis=0)
        elif isinstance(sample, numbers.Number):
            batch = np.array(batch)
            return batch
        elif isinstance(sample, (str, bytes)):
            return batch
        elif isinstance(sample, Mapping):
            # 修复：检查所有样本是否有相同的键
            try:
                all_keys = [set(d.keys()) for d in batch]
                if not all(keys == all_keys[0] for keys in all_keys):
                    # 使用所有样本共有的键
                    common_keys = set.intersection(*all_keys)
                    if not common_keys:
                        # 如果没有共同键，使用第一个样本的键
                        common_keys = all_keys[0]
                    # 只处理存在该键的样本
                    result = {}
                    for key in common_keys:
                        key_batch = [d[key] for d in batch if key in d]
                        if key_batch:  # 确保有数据
                            result[key] = self(key_batch)
                    return result
                else:
                    # 正常情况：所有样本有相同的键
                    return {key: self([d[key] for d in batch]) for key in sample}
            except Exception as e:
                # 如果出现任何错误，返回原始批次
                print(f"Warning: Error in mapping collation: {e}")
                return batch
        elif isinstance(sample, Sequence):
            sample_fields_num = len(sample)
            # 修复：检查序列长度一致性
            field_lengths = [len(s) for s in batch]
            if not all(length == sample_fields_num for length in field_lengths):
                # 使用最常见的长度
                from collections import Counter
                length_counts = Counter(field_lengths)
                most_common_length = length_counts.most_common(1)[0][0]
                # 过滤到最常见长度的样本
                filtered_batch = [s for s in batch if len(s) == most_common_length]
                if len(filtered_batch) >= len(batch) * 0.5:  # 至少保留一半样本
                    return [self(fields) for fields in zip(*filtered_batch)]
                else:
                    # 如果损失太多样本，截断到最小长度
                    min_length = min(field_lengths)
                    truncated_batch = [s[:min_length] for s in batch]
                    return [self(fields) for fields in zip(*truncated_batch)]
            return [self(fields) for fields in zip(*batch)]
        elif str(type(sample)) == "<class 'pgl.graph.Graph'>":
            # use str(type()) instead of isinstance() in case of pgl is not installed.
            graphs = pgl.Graph.batch(batch)
            # NOTE: when num_works >1, graphs.tensor() will convert numpy.ndarray to
            # CPU Tensor, which will cause error in model training.
            # graphs.tensor()
            return graphs
        elif isinstance(sample, ConcatData):
            return ConcatData.batch(batch)
        raise TypeError(
            "batch data can only contains: paddle.Tensor, numpy.ndarray, "
            f"dict, list, number, None, pgl.Graph, but got {type(sample)}"
        )
