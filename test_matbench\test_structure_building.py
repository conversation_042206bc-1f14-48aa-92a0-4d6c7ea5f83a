#!/usr/bin/env python3
"""
测试结构构建模块
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_structure_building():
    """测试结构构建功能"""
    print("🔍 测试结构构建模块...")
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        from ppmat.datasets.build_structure import BuildStructure
        
        # 创建数据集实例
        dataset = MatBenchDataset.__new__(MatBenchDataset)
        dataset.path = "./data/matbench/matbench_mp_e_form_full.pkl"
        dataset.max_samples = 3
        dataset.cache_path = "./data/matbench_cache/test_structure"
        dataset.overwrite = True
        
        # 读取数据
        data, num_samples = dataset.read_data(dataset.path)
        dataset.row_data = data['structure']
        dataset.num_samples = num_samples
        
        # 设置结构构建配置
        build_structure_cfg = {
            "format": "pymatgen_structure",
            "primitive": False,
            "niggli": True,
            "num_cpus": 1,
        }
        dataset.build_structure = BuildStructure(**build_structure_cfg)
        
        # 测试结构构建
        structures = dataset.build_structures(overwrite=True)
        
        print(f"✅ 结构构建成功")
        print(f"  结构数量: {len(structures)}")
        print(f"  结构路径示例: {structures[0]}")
        
        # 验证结构文件
        assert len(structures) == num_samples, "结构数量不匹配"
        assert all(os.path.exists(s) for s in structures), "结构文件不存在"
        
        # 测试加载结构
        with open(structures[0], 'rb') as f:
            import pickle
            structure = pickle.load(f)
        
        print(f"  结构类型: {type(structure)}")
        print(f"  原子数量: {len(structure) if hasattr(structure, '__len__') else 'N/A'}")
        
        print("✅ 结构构建验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 结构构建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 结构构建模块测试")
    print("=" * 30)
    
    # 检查数据文件
    data_file = project_root / "data" / "matbench" / "matbench_mp_e_form_full.pkl"
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    if test_structure_building():
        print("\n🎉 结构构建模块测试通过!")
    else:
        print("\n❌ 结构构建模块测试失败")

if __name__ == "__main__":
    main()
