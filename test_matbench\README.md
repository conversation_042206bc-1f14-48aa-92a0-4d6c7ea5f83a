# MatBench 测试文件夹

这个文件夹包含了用于测试MatBench数据集集成的所有文件。

## 📁 文件结构

```
test_matbench/
├── README.md                 # 说明文档（本文件）
├── download_matbench.py      # 下载完整MatBench数据集
├── verify_setup.py           # 验证环境和设置
├── test_config_1000.yaml     # 1000样本训练配置
└── test_matbench_1000.py     # 完整测试流程
```

## 🚀 使用步骤

### 1. 验证环境
```bash
python test_matbench/verify_setup.py
```

### 2. 下载数据（如果需要）
```bash
python test_matbench/download_matbench.py
```

### 3. 运行完整测试
```bash
python test_matbench/test_matbench_1000.py
```

## 🎯 设计理念

### 核心原则
1. **不修改原始数据** - 下载完整的MatBench数据集，保持原始格式
2. **在数据集类中限制** - 通过`max_samples`参数在读取时限制样本数量
3. **模仿MP2018风格** - 参考`ppmat/datasets/mp2018_dataset.py`的实现风格
4. **独立测试环境** - 所有测试文件放在`test_matbench/`文件夹中

### 数据流程
```
MatBench原始数据 → MatBenchDataset类 → max_samples限制 → 前1000个样本
```

## 📊 预期结果

### 数据集信息
- **完整数据集**: 132,752个样本
- **测试使用**: 前1000个样本
- **数据质量**: 官方原始数据，无重复或修改

### 训练性能
- **训练时间**: 1-2小时
- **预期MAE**: 0.08-0.12 eV/atom
- **相比小规模数据**: 显著性能提升

## 🔧 配置说明

### test_config_1000.yaml
- 使用`MatBenchDataset`类
- 设置`max_samples: 1000`限制样本数量
- 配置适合1000样本的训练参数
- 输出到`test_matbench/output/`目录

### MatBenchDataset类特点
- 支持`max_samples`参数
- 自动处理缓存
- 兼容现有的图转换器
- 遵循MP2018Dataset的设计模式

## 🧪 测试内容

### verify_setup.py
- 检查环境依赖
- 验证数据集类
- 测试小规模数据加载
- 验证配置文件

### test_matbench_1000.py
- 完整的训练流程测试
- 结果分析
- 预测功能测试
- 性能评估

## 📈 性能对比

| 数据规模 | 样本数 | 数据来源 | 预期MAE | 训练时间 |
|----------|--------|----------|---------|----------|
| 测试集 | 200 | 重复数据 | 0.15-0.25 | 30分钟 |
| 中等规模 | 500 | 重复数据 | 0.12-0.18 | 1小时 |
| **本测试** | **1000** | **官方原始** | **0.08-0.12** | **1-2小时** |
| 完整数据集 | 132k | 官方原始 | 0.03-0.06 | 2-3天 |

## 🎯 优势

1. **高质量数据**: 使用官方MatBench数据，无数据质量问题
2. **灵活规模**: 通过`max_samples`参数轻松调整数据规模
3. **标准流程**: 遵循PaddleMaterial的标准数据集实现模式
4. **易于扩展**: 可以轻松扩展到其他MatBench任务

## 🔄 扩展使用

### 调整样本数量
```yaml
# 在配置文件中修改
Dataset:
  train:
    dataset:
      __init_params__:
        max_samples: 2000  # 使用前2000个样本
```

### 使用其他MatBench任务
```python
# 下载其他任务数据后
dataset = MatBenchDataset(
    path="./data/matbench",
    task_name="matbench_mp_gap",  # 其他任务
    property_name="gap",
    max_samples=1000
)
```

## 📝 注意事项

1. 首次运行需要下载数据，可能需要10-20分钟
2. 训练过程中会生成缓存文件，可以加速后续运行
3. 如果修改了图转换参数，建议设置`overwrite=True`重新生成缓存
4. 所有输出文件都在`test_matbench/output/`目录中，不会影响其他实验
