hydra:
  run:
    dir: ${oc.env:OUTPUT_DIR,outputs/singlerun/${now:%Y-%m-%d}/${now:%H-%M-%S}}

defaults:
  - data_module: mp_20
  - trainer: default
  - lightning_module: default
  - adapter: default

trainer:
  max_epochs: 200
  logger:
    job_type: train_finetune # override default defined in defaults.trainer yaml file

lightning_module:
  optimizer_partial:
    # lr: 5e-6
    # for compatibility with paddle
    cfg:
      lr: 
        learning_rate: 5e-6
