#!/usr/bin/env python3
"""
测试图转换功能
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_graph_conversion():
    """测试图转换功能"""
    print("🔍 测试图转换功能...")
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        
        # 创建数据集，强制重新构建图
        dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_full",
            property_names=["e_form"],
            max_samples=3,  # 只测试3个样本
            overwrite=True,  # 强制重新构建
            build_graph_cfg={
                "__class_name__": "ComformerGraphConverter",
                "__init_params__": {
                    "cutoff": 5.0,
                    "num_cpus": 1,
                    "atom_features": "cgcnn",
                    "max_neighbors": 25
                }
            }
        )
        
        print(f"✅ 数据集创建成功，样本数: {len(dataset)}")
        
        # 测试每个样本
        for i in range(len(dataset)):
            try:
                sample = dataset[i]
                print(f"  样本 {i}:")
                print(f"    键: {list(sample.keys())}")
                
                graph = sample.get('graph')
                if graph is not None:
                    print(f"    图类型: {type(graph)}")
                    if hasattr(graph, 'num_nodes'):
                        print(f"    节点数: {graph.num_nodes}")
                    if hasattr(graph, 'num_edges'):
                        print(f"    边数: {graph.num_edges}")
                    print(f"    ✅ 图转换成功")
                else:
                    print(f"    ❌ 图为None")
                
                e_form = sample.get('e_form')
                print(f"    e_form: {e_form} ({type(e_form)})")
                
            except Exception as e:
                print(f"  ❌ 样本 {i} 处理失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 图转换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_graph_converter():
    """直接测试图转换器"""
    print("\n🔍 直接测试图转换器...")
    
    try:
        from ppmat.models import build_graph_converter
        import pickle
        
        # 加载一个结构
        with open("./data/matbench/matbench_mp_e_form_full.pkl", 'rb') as f:
            data = pickle.load(f)
        
        # 获取第一个结构
        structures = data['graphs']
        first_structure = structures[0]
        
        print(f"  结构类型: {type(first_structure)}")
        print(f"  结构信息: {first_structure}")
        
        # 创建图转换器
        graph_converter = build_graph_converter({
            "__class_name__": "ComformerGraphConverter",
            "__init_params__": {
                "cutoff": 5.0,
                "num_cpus": 1,
                "atom_features": "cgcnn",
                "max_neighbors": 25
            }
        })
        
        print(f"  图转换器类型: {type(graph_converter)}")
        
        # 转换图
        graph = graph_converter(first_structure)
        
        print(f"  转换后图类型: {type(graph)}")
        if hasattr(graph, 'num_nodes'):
            print(f"  节点数: {graph.num_nodes}")
        if hasattr(graph, 'num_edges'):
            print(f"  边数: {graph.num_edges}")
        if hasattr(graph, 'node_feat'):
            node_feat = graph.node_feat
            if hasattr(node_feat, 'shape'):
                print(f"  节点特征形状: {node_feat.shape}")
            else:
                print(f"  节点特征类型: {type(node_feat)}")
        if hasattr(graph, 'edge_feat'):
            edge_feat = graph.edge_feat
            if hasattr(edge_feat, 'shape'):
                print(f"  边特征形状: {edge_feat.shape}")
            else:
                print(f"  边特征类型: {type(edge_feat)}")
        
        print("✅ 直接图转换成功")
        return True
        
    except Exception as e:
        print(f"❌ 直接图转换失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 图转换功能测试")
    print("=" * 40)
    
    # 检查数据文件
    data_file = project_root / "data" / "matbench" / "matbench_mp_e_form_full.pkl"
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    tests = [
        ("直接图转换器测试", test_direct_graph_converter),
        ("数据集图转换测试", test_graph_conversion),
    ]
    
    all_passed = True
    
    for i, (test_name, test_func) in enumerate(tests, 1):
        print(f"\n测试 {i}/{len(tests)}: {test_name}")
        print("-" * 30)
        
        if not test_func():
            all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 图转换测试通过!")
        print("现在可以正常使用图转换功能了")
    else:
        print("❌ 图转换测试失败")
        print("可能需要检查图转换器配置")

if __name__ == "__main__":
    main()
