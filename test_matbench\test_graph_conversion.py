#!/usr/bin/env python3
"""
测试图转换模块
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_graph_conversion():
    """测试图转换功能"""
    print("🔍 测试图转换模块...")
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        from ppmat.datasets.build_structure import BuildStructure
        from ppmat.models import build_graph_converter
        
        # 创建数据集实例
        dataset = MatBenchDataset.__new__(MatBenchDataset)
        dataset.path = "./data/matbench/matbench_mp_e_form_full.pkl"
        dataset.max_samples = 2
        dataset.cache_path = "./data/matbench_cache/test_graph"
        dataset.overwrite = True
        
        # 读取数据并构建结构
        data, num_samples = dataset.read_data(dataset.path)
        dataset.row_data = data['structure']
        dataset.num_samples = num_samples
        
        # 构建结构
        build_structure_cfg = {
            "format": "pymatgen_structure",
            "primitive": False,
            "niggli": True,
            "num_cpus": 1,
        }
        dataset.build_structure = BuildStructure(**build_structure_cfg)
        dataset.structures = dataset.build_structures(overwrite=True)
        
        # 设置图转换器
        build_graph_cfg = {
            "__class_name__": "ComformerGraphConverter",
            "__init_params__": {
                "cutoff": 5.0,
                "num_cpus": 1,
                "atom_features": "cgcnn",
                "max_neighbors": 25
            }
        }
        dataset.graph_converter = build_graph_converter(build_graph_cfg)
        
        # 测试图转换
        graphs = dataset.build_graphs(overwrite=True)
        
        print(f"✅ 图转换成功")
        print(f"  图数量: {len(graphs)}")
        print(f"  图路径示例: {graphs[0]}")
        
        # 验证图文件
        assert len(graphs) == num_samples, "图数量不匹配"
        assert all(os.path.exists(g) for g in graphs), "图文件不存在"
        
        # 测试加载图
        with open(graphs[0], 'rb') as f:
            import pickle
            graph = pickle.load(f)
        
        print(f"  图类型: {type(graph)}")
        if hasattr(graph, 'num_nodes'):
            print(f"  节点数: {graph.num_nodes}")
        if hasattr(graph, 'num_edges'):
            print(f"  边数: {graph.num_edges}")
        
        print("✅ 图转换验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 图转换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 图转换模块测试")
    print("=" * 30)
    
    # 检查数据文件
    data_file = project_root / "data" / "matbench" / "matbench_mp_e_form_full.pkl"
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    if test_graph_conversion():
        print("\n🎉 图转换模块测试通过!")
    else:
        print("\n❌ 图转换模块测试失败")

if __name__ == "__main__":
    main()
