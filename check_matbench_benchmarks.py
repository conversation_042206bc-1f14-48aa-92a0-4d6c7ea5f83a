#!/usr/bin/env python3
"""
检查MatBench官方基准指标
"""
import numpy as np

def show_matbench_benchmarks():
    """显示MatBench官方基准"""
    
    print("📊 MatBench官方基准指标 (matbench_mp_e_form)")
    print("="*60)
    
    # 官方基准数据 (来源: MatBench论文和排行榜)
    benchmarks = {
        "CGCNN": {"MAE": 0.039, "RMSE": 0.054},
        "MEGNet": {"MAE": 0.028, "RMSE": 0.042},
        "SchNet": {"MAE": 0.035, "RMSE": 0.049},
        "CGCNN+P": {"MAE": 0.034, "RMSE": 0.048},
        "Roost": {"MAE": 0.037, "RMSE": 0.052},
        "Wren": {"MAE": 0.051, "RMSE": 0.071},
        "ElemNet": {"MAE": 0.068, "RMSE": 0.093},
        "Random Forest": {"MAE": 0.082, "RMSE": 0.111}
    }
    
    print("模型名称          MAE (eV/atom)    RMSE (eV/atom)")
    print("-" * 50)
    
    for model, metrics in benchmarks.items():
        print(f"{model:<15} {metrics['MAE']:<12.3f} {metrics['RMSE']:<12.3f}")
    
    print("\n🎯 分析:")
    print(f"• 最佳性能: MEGNet (MAE: 0.028 eV/atom)")
    print(f"• 平均性能: ~0.040 eV/atom")
    print(f"• 我们的结果: 0.123 eV/atom")
    print(f"• 性能差距: 约3-4倍")

def analyze_our_results():
    """分析我们的训练结果"""
    
    print("\n🔍 我们的结果分析")
    print("="*60)
    
    our_results = {
        "200样本": {"预期MAE": "0.10-0.20", "实际情况": "未知"},
        "500样本": {"预期MAE": "0.08-0.15", "实际MAE": "0.123"},
        "完整数据集": {"预期MAE": "0.03-0.06", "实际情况": "未训练"}
    }
    
    print("数据规模        预期MAE         实际MAE")
    print("-" * 45)
    
    for scale, metrics in our_results.items():
        actual = metrics.get('实际MAE', metrics.get('实际情况', 'N/A'))
        print(f"{scale:<12} {metrics['预期MAE']:<12} {actual}")
    
    print("\n⚠️ 问题诊断:")
    problems = [
        "1. 数据规模太小 - 200/500样本 vs 官方132k样本",
        "2. 数据重复使用 - 可能导致过拟合",
        "3. 模型未充分优化 - 可能需要调参",
        "4. 训练轮数不足 - 可能未完全收敛",
        "5. 数据预处理差异 - 与官方方法可能不同"
    ]
    
    for problem in problems:
        print(f"  {problem}")

def suggest_improvements():
    """建议改进方案"""
    
    print("\n💡 改进建议")
    print("="*60)
    
    improvements = {
        "数据方面": [
            "使用完整的132k样本数据集",
            "确保数据预处理与官方一致",
            "检查数据分割的随机性",
            "验证数据标准化方法"
        ],
        "模型方面": [
            "增加训练轮数 (200+ epochs)",
            "调整学习率调度策略",
            "尝试不同的模型超参数",
            "使用更深的网络结构"
        ],
        "训练方面": [
            "使用更大的批次大小",
            "启用数据增强技术",
            "实施早停策略",
            "使用集成学习方法"
        ],
        "验证方面": [
            "使用k折交叉验证",
            "在独立测试集上评估",
            "对比多个随机种子结果",
            "分析预测误差分布"
        ]
    }
    
    for category, suggestions in improvements.items():
        print(f"\n🔧 {category}:")
        for suggestion in suggestions:
            print(f"  • {suggestion}")

def create_improved_config():
    """创建改进的配置文件"""
    
    print("\n📝 创建改进配置")
    print("="*60)
    
    config_content = """# 改进的Comformer配置 - 针对更好的性能
Global:
  label_names:
  - e_form
  do_train: true
  do_eval: true
  do_test: true
  split_dataset_ratio:
    train: 0.8
    val: 0.1
    test: 0.1
  graph_converter:
    __class_name__: ComformerGraphConverter
    __init_params__:
      cutoff: 5.0
      num_cpus: 8
      atom_features: cgcnn
      max_neighbors: 25

Trainer:
  max_epochs: 200          # 增加训练轮数
  seed: 42
  output_dir: ./output/comformer_matbench_improved
  save_freq: 50
  log_freq: 20
  start_eval_epoch: 10
  eval_freq: 10
  pretrained_model_path: null
  resume_from_checkpoint: null
  use_amp: true            # 启用混合精度
  amp_level: O1
  eval_with_no_grad: true
  gradient_accumulation_steps: 4  # 增加梯度累积
  best_metric_indicator: eval_metric
  name_for_best_metric: e_form
  greater_is_better: false
  compute_metric_during_train: true
  metric_strategy_during_eval: epoch

Model:
  __class_name__: iComformer
  __init_params__:
    conv_layers: 6           # 增加层数
    edge_layers: 2           # 增加边层数
    atom_input_features: 92
    edge_features: 512       # 增加特征维度
    triplet_input_features: 512
    node_features: 512
    fc_features: 512
    output_features: 1
    node_layer_head: 2       # 增加注意力头数
    edge_layer_head: 2
    property_name: ${Global.label_names}

Optimizer:
  __class_name__: AdamW
  __init_params__:
    lr:
      __class_name__: CosineAnnealingLR  # 改用余弦退火
      __init_params__:
        T_max: 200
        eta_min: 1e-6
    weight_decay: 1e-4       # 添加权重衰减

Dataset:
  train:
    dataset:
      __class_name__: MatBenchDataset
      __init_params__:
        path: ./data/matbench
        task_name: matbench_mp_e_form_full  # 使用完整数据集
        property_name: e_form
        overwrite: false
        transforms:
        - __class_name__: ComformerCompatTransform
          __init_params__: {}
    num_workers: 8
    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: true
        drop_last: true
        batch_size: 128        # 增加批次大小
"""
    
    config_file = "property_prediction/configs/comformer/comformer_matbench_improved.yaml"
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        print(f"✅ 改进配置已保存: {config_file}")
        
        print("\n🚀 使用改进配置训练:")
        print(f"python property_prediction/train.py -c {config_file}")
        
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")

def main():
    """主函数"""
    print("🔍 MatBench验证指标分析")
    print("="*60)
    
    show_matbench_benchmarks()
    analyze_our_results()
    suggest_improvements()
    
    print("\n" + "="*60)
    choice = input("是否创建改进的配置文件? (y/n): ").lower().strip()
    
    if choice == 'y':
        create_improved_config()
    
    print("\n📋 总结:")
    print("• 当前指标确实偏高，有改进空间")
    print("• 主要问题是数据规模和模型优化")
    print("• 建议使用完整数据集和改进配置")
    print("• 预期改进后MAE可降至0.03-0.06 eV/atom")

if __name__ == "__main__":
    main()
