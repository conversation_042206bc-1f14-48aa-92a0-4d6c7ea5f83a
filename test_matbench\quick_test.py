#!/usr/bin/env python3
"""
快速验证MatBench数据集修复效果
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def quick_test():
    """快速测试"""
    print("🔍 快速验证MatBench数据集...")
    
    try:
        from ppmat.datasets.matbench_dataset import MatBenchDataset
        
        # 最简单的配置
        dataset = MatBenchDataset(
            path="./data/matbench",
            task_name="matbench_mp_e_form_full",
            property_names=["e_form"],
            max_samples=3,
            overwrite=False,  # 使用缓存
            build_graph_cfg=None  # 不构建图
        )
        
        print(f"✅ 数据集加载成功，样本数: {len(dataset)}")
        
        # 测试批处理兼容性
        samples = []
        for i in range(len(dataset)):
            sample = dataset[i]
            samples.append(sample)
            print(f"  样本 {i}: 键={list(sample.keys())}, e_form={sample['e_form']}")
        
        # 检查一致性
        if samples:
            first_keys = set(samples[0].keys())
            all_consistent = all(set(sample.keys()) == first_keys for sample in samples)
            
            if all_consistent:
                print("✅ 所有样本的键一致")
                
                # 检查数据类型
                for key in first_keys:
                    types = [type(sample[key]) for sample in samples]
                    if len(set(str(t) for t in types)) == 1:
                        print(f"  ✅ 键 '{key}' 的数据类型一致: {types[0]}")
                    else:
                        print(f"  ❌ 键 '{key}' 的数据类型不一致: {types}")
                        return False
                
                print("🎉 批处理兼容性测试通过!")
                return True
            else:
                print("❌ 样本的键不一致")
                return False
        
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 MatBench快速验证")
    print("=" * 30)
    
    # 检查数据文件
    data_file = project_root / "data" / "matbench" / "matbench_mp_e_form_full.pkl"
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    if quick_test():
        print("\n🎉 验证成功!")
        print("MatBench数据集已修复，可以正常使用")
        print("\n下一步:")
        print("• 运行完整训练: python test_matbench/test_matbench_1000.py")
        print("• 或使用配置: python property_prediction/train.py -c test_matbench/test_config_1000.yaml")
    else:
        print("\n❌ 验证失败")

if __name__ == "__main__":
    main()
