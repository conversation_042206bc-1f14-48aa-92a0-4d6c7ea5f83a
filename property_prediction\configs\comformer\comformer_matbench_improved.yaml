# 改进的Comformer配置 - 针对更好的性能
Global:
  label_names:
  - e_form
  do_train: true
  do_eval: true
  do_test: true
  split_dataset_ratio:
    train: 0.8
    val: 0.1
    test: 0.1
  graph_converter:
    __class_name__: ComformerGraphConverter
    __init_params__:
      cutoff: 5.0
      num_cpus: 8
      atom_features: cgcnn
      max_neighbors: 25

Trainer:
  max_epochs: 200          # 增加训练轮数
  seed: 42
  output_dir: ./output/comformer_matbench_improved
  save_freq: 50
  log_freq: 20
  start_eval_epoch: 10
  eval_freq: 10
  pretrained_model_path: null
  resume_from_checkpoint: null
  use_amp: true            # 启用混合精度
  amp_level: O1
  eval_with_no_grad: true
  gradient_accumulation_steps: 4  # 增加梯度累积
  best_metric_indicator: eval_metric
  name_for_best_metric: e_form
  greater_is_better: false
  compute_metric_during_train: true
  metric_strategy_during_eval: epoch

Model:
  __class_name__: iComformer
  __init_params__:
    conv_layers: 6           # 增加层数
    edge_layers: 2           # 增加边层数
    atom_input_features: 92
    edge_features: 512       # 增加特征维度
    triplet_input_features: 512
    node_features: 512
    fc_features: 512
    output_features: 1
    node_layer_head: 2       # 增加注意力头数
    edge_layer_head: 2
    property_name: ${Global.label_names}

Optimizer:
  __class_name__: AdamW
  __init_params__:
    lr:
      __class_name__: CosineAnnealingLR  # 改用余弦退火
      __init_params__:
        T_max: 200
        eta_min: 1e-6
    weight_decay: 1e-4       # 添加权重衰减

Dataset:
  train:
    dataset:
      __class_name__: MatBenchDataset
      __init_params__:
        path: ./data/matbench
        task_name: matbench_mp_e_form_full  # 使用完整数据集
        property_name: e_form
        overwrite: false
        transforms:
        - __class_name__: ComformerCompatTransform
          __init_params__: {}
    num_workers: 8
    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: true
        drop_last: true
        batch_size: 128        # 增加批次大小
