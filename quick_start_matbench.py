#!/usr/bin/env python3
"""
MatBench完整数据集快速开始脚本
"""
import os
import sys
import subprocess
from pathlib import Path

def run_command(cmd, description, timeout=300):
    """运行命令并显示进度"""
    print(f"\n{'='*60}")
    print(f"执行: {description}")
    print(f"命令: {cmd}")
    print('='*60)
    
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            cwd=os.getcwd()
        )
        
        if result.returncode == 0:
            print("✓ 执行成功")
            if result.stdout:
                print("输出:")
                print(result.stdout)
            return True
        else:
            print("✗ 执行失败")
            if result.stderr:
                print("错误信息:")
                print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print(f"✗ 执行超时 (>{timeout}秒)")
        return False
    except Exception as e:
        print(f"✗ 执行异常: {e}")
        return False

def check_environment():
    """检查环境配置"""
    print("检查环境配置...")
    
    # 检查Python环境
    python_version = sys.version_info
    if python_version.major < 3 or python_version.minor < 8:
        print("✗ Python版本过低，需要Python 3.8+")
        return False
    print(f"✓ Python版本: {python_version.major}.{python_version.minor}")
    
    # 检查必要的包
    required_packages = ['paddle', 'matbench', 'pymatgen']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"✗ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n需要安装以下包: {', '.join(missing_packages)}")
        print("运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def show_menu():
    """显示菜单选项"""
    print("\n" + "="*60)
    print("MatBench完整数据集快速开始")
    print("="*60)
    print("请选择操作:")
    print("1. 下载完整MatBench数据集")
    print("2. 验证现有数据集")
    print("3. 开始训练 (测试数据集 - 200样本)")
    print("4. 开始训练 (中等数据集 - 500样本)")
    print("5. 开始训练 (完整数据集 - 全部样本)")
    print("6. 查看训练日志")
    print("7. 清理缓存文件")
    print("0. 退出")
    print("="*60)

def download_data():
    """下载数据"""
    print("开始下载MatBench完整数据集...")
    return run_command(
        "python download_matbench_full.py",
        "下载完整数据集",
        timeout=1800  # 30分钟超时
    )

def verify_data():
    """验证数据"""
    print("验证数据集完整性...")
    return run_command(
        "python verify_matbench_data.py",
        "验证数据集",
        timeout=300
    )

def start_training(config_name, description):
    """开始训练"""
    config_path = f"property_prediction/configs/comformer/{config_name}"
    
    if not os.path.exists(config_path):
        print(f"✗ 配置文件不存在: {config_path}")
        return False
    
    cmd = f"python property_prediction/train.py -c {config_path}"
    print(f"开始训练: {description}")
    print("注意: 训练过程可能需要很长时间，建议在后台运行")
    print(f"命令: {cmd}")
    
    # 询问用户是否要在后台运行
    choice = input("是否在后台运行训练? (y/n): ").lower().strip()
    
    if choice == 'y':
        # 后台运行
        log_file = f"training_{config_name.replace('.yaml', '').replace('/', '_')}.log"
        cmd_bg = f"nohup {cmd} > {log_file} 2>&1 &"
        
        try:
            subprocess.Popen(cmd_bg, shell=True)
            print(f"✓ 训练已在后台启动")
            print(f"日志文件: {log_file}")
            print(f"查看日志: tail -f {log_file}")
            return True
        except Exception as e:
            print(f"✗ 后台启动失败: {e}")
            return False
    else:
        # 前台运行
        return run_command(cmd, f"训练 - {description}", timeout=None)

def view_logs():
    """查看训练日志"""
    print("查找训练日志文件...")
    
    # 查找输出目录
    output_dirs = []
    if os.path.exists("output"):
        for item in os.listdir("output"):
            if item.startswith("comformer_matbench"):
                output_dirs.append(os.path.join("output", item))
    
    # 查找日志文件
    log_files = []
    for pattern in ["training_*.log", "*.log"]:
        import glob
        log_files.extend(glob.glob(pattern))
    
    # 查找输出目录中的日志
    for output_dir in output_dirs:
        log_file = os.path.join(output_dir, "run.log")
        if os.path.exists(log_file):
            log_files.append(log_file)
    
    if not log_files:
        print("✗ 未找到日志文件")
        return False
    
    print("找到以下日志文件:")
    for i, log_file in enumerate(log_files, 1):
        print(f"  {i}. {log_file}")
    
    try:
        choice = int(input("请选择要查看的日志文件 (输入数字): "))
        if 1 <= choice <= len(log_files):
            selected_log = log_files[choice - 1]
            print(f"查看日志: {selected_log}")
            
            # 显示最后50行
            cmd = f"tail -50 {selected_log}"
            return run_command(cmd, f"查看日志 - {selected_log}", timeout=30)
        else:
            print("✗ 无效选择")
            return False
    except ValueError:
        print("✗ 请输入有效数字")
        return False

def clean_cache():
    """清理缓存文件"""
    print("清理缓存文件...")
    
    cache_patterns = [
        "./data/matbench/*_cache*",
        "./data/matbench_*_cache*",
        "./__pycache__",
        "./ppmat/__pycache__",
        "*.pyc"
    ]
    
    import glob
    import shutil
    
    cleaned = 0
    for pattern in cache_patterns:
        files = glob.glob(pattern, recursive=True)
        for file_path in files:
            try:
                if os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                    print(f"✓ 删除目录: {file_path}")
                else:
                    os.remove(file_path)
                    print(f"✓ 删除文件: {file_path}")
                cleaned += 1
            except Exception as e:
                print(f"✗ 删除失败 {file_path}: {e}")
    
    print(f"✓ 清理完成，共清理 {cleaned} 个文件/目录")
    return True

def main():
    """主函数"""
    # 检查环境
    if not check_environment():
        print("环境检查失败，请先安装必要的依赖包")
        sys.exit(1)
    
    while True:
        show_menu()
        
        try:
            choice = input("请输入选择 (0-7): ").strip()
            
            if choice == '0':
                print("退出程序")
                break
            elif choice == '1':
                download_data()
            elif choice == '2':
                verify_data()
            elif choice == '3':
                start_training(
                    "comformer_matbench_mp_e_form_test.yaml",
                    "测试数据集 (200样本)"
                )
            elif choice == '4':
                start_training(
                    "comformer_matbench_mp_e_form_500.yaml", 
                    "中等数据集 (500样本)"
                )
            elif choice == '5':
                start_training(
                    "comformer_matbench_mp_e_form_full.yaml",
                    "完整数据集 (全部样本)"
                )
            elif choice == '6':
                view_logs()
            elif choice == '7':
                clean_cache()
            else:
                print("✗ 无效选择，请输入0-7之间的数字")
                
        except KeyboardInterrupt:
            print("\n\n用户中断，退出程序")
            break
        except Exception as e:
            print(f"✗ 发生错误: {e}")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
