Global:
  label_names:
  - e_form
  do_train: true
  do_eval: true
  do_test: true
  split_dataset_ratio:
    train: 0.8
    val: 0.1
    test: 0.1
  graph_converter:
    __class_name__: ComformerGraphConverter
    __init_params__:
      cutoff: 5.0
      num_cpus: 4
      atom_features: cgcnn
      max_neighbors: 25
Trainer:
  max_epochs: 100
  seed: 42
  output_dir: ./test_matbench/output/comformer_1000_samples_t_20250724_101321_s_42
  save_freq: 25
  log_freq: 10
  start_eval_epoch: 10
  eval_freq: 10
  pretrained_model_path: null
  resume_from_checkpoint: null
  use_amp: true
  amp_level: O1
  eval_with_no_grad: true
  gradient_accumulation_steps: 1
  best_metric_indicator: eval_metric
  name_for_best_metric: e_form
  greater_is_better: false
  compute_metric_during_train: true
  metric_strategy_during_eval: epoch
  use_visualdl: false
  use_wandb: false
  use_tensorboard: false
Model:
  __class_name__: iComformer
  __init_params__:
    conv_layers: 4
    edge_layers: 1
    atom_input_features: 92
    edge_features: 256
    triplet_input_features: 256
    node_features: 256
    fc_features: 256
    output_features: 1
    node_layer_head: 2
    edge_layer_head: 2
    property_name: ${Global.label_names}
Metric:
  e_form:
    __class_name__: paddle.nn.L1Loss
    __init_params__: {}
Optimizer:
  __class_name__: AdamW
  __init_params__:
    lr:
      __class_name__: OneCycleLR
      __init_params__:
        max_learning_rate: 0.001
    weight_decay: 1.0e-05
Dataset:
  train:
    dataset:
      __class_name__: MatBenchDataset
      __init_params__:
        path: ./data/matbench
        task_name: matbench_mp_e_form_full
        property_name: e_form
        overwrite: false
        max_samples: 1000
        build_graph_cfg: ${Global.graph_converter}
        transforms:
        - __class_name__: ComformerCompatTransform
          __init_params__: {}
    num_workers: 0
    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: true
        drop_last: true
        batch_size: 32
  val:
    dataset:
      __class_name__: MatBenchDataset
      __init_params__:
        path: ./data/matbench
        task_name: matbench_mp_e_form_full
        property_name: e_form
        overwrite: false
        max_samples: 1000
        build_graph_cfg: ${Global.graph_converter}
        transforms:
        - __class_name__: ComformerCompatTransform
          __init_params__: {}
    num_workers: 0
    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: false
        drop_last: false
        batch_size: 32
  test:
    dataset:
      __class_name__: MatBenchDataset
      __init_params__:
        path: ./data/matbench
        task_name: matbench_mp_e_form_full
        property_name: e_form
        overwrite: false
        max_samples: 1000
        build_graph_cfg: ${Global.graph_converter}
        transforms:
        - __class_name__: ComformerCompatTransform
          __init_params__: {}
    num_workers: 0
    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: false
        drop_last: false
        batch_size: 32
Predict:
  graph_converter: ${Global.graph_converter}
  eval_with_no_grad: true
