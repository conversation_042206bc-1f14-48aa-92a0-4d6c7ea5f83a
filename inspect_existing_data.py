#!/usr/bin/env python3
"""
检查现有的matbench_mp_e_form_processed.pkl文件
"""
import pickle
import os

def inspect_processed_data():
    """检查processed数据文件的详细结构"""
    
    file_path = "./data/matbench/matbench_mp_e_form_processed.pkl"
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return
    
    print(f"检查文件: {file_path}")
    print("=" * 60)
    
    try:
        with open(file_path, 'rb') as f:
            data = pickle.load(f)
        
        print(f"数据类型: {type(data)}")
        
        if isinstance(data, dict):
            print(f"字典键: {list(data.keys())}")
            
            for key, value in data.items():
                print(f"\n键 '{key}':")
                print(f"  类型: {type(value)}")
                
                if hasattr(value, '__len__'):
                    try:
                        print(f"  长度: {len(value)}")
                    except:
                        print("  长度: 无法获取")
                
                if isinstance(value, dict):
                    print(f"  子键: {list(value.keys())[:10]}...")  # 只显示前10个键
                elif isinstance(value, list):
                    if len(value) > 0:
                        print(f"  第一个元素类型: {type(value[0])}")
                        if hasattr(value[0], '__dict__'):
                            print(f"  第一个元素属性: {list(value[0].__dict__.keys())[:5]}...")
                elif hasattr(value, 'shape'):
                    print(f"  形状: {value.shape}")
                
                # 如果是数值列表，显示统计信息
                if isinstance(value, list) and len(value) > 0:
                    try:
                        if isinstance(value[0], (int, float)):
                            print(f"  数值范围: {min(value):.3f} ~ {max(value):.3f}")
                            print(f"  平均值: {sum(value)/len(value):.3f}")
                    except:
                        pass
        
        elif isinstance(data, list):
            print(f"列表长度: {len(data)}")
            if len(data) > 0:
                print(f"第一个元素类型: {type(data[0])}")
                if isinstance(data[0], dict):
                    print(f"第一个元素的键: {list(data[0].keys())}")
        
        else:
            print(f"数据内容: {str(data)[:200]}...")
            
    except Exception as e:
        print(f"读取文件时出错: {e}")

def convert_to_standard_format():
    """将processed数据转换为标准格式"""
    
    input_file = "./data/matbench/matbench_mp_e_form_processed.pkl"
    output_file = "./data/matbench/matbench_mp_e_form_full.pkl"
    
    if not os.path.exists(input_file):
        print(f"输入文件不存在: {input_file}")
        return False
    
    try:
        print("正在转换数据格式...")
        
        with open(input_file, 'rb') as f:
            data = pickle.load(f)
        
        print(f"原始数据类型: {type(data)}")
        
        # 根据实际数据结构进行转换
        if isinstance(data, dict):
            if 'graphs' in data and 'props' in data:
                # 已经是正确格式，但props可能不是字典
                graphs = data['graphs']
                props = data['props']
                
                # 检查props格式
                if not isinstance(props, dict):
                    print(f"转换props格式: {type(props)} -> dict")
                    if isinstance(props, list):
                        # 假设是e_form值的列表
                        props = {"e_form": props}
                    else:
                        print(f"无法处理的props类型: {type(props)}")
                        return False
                
                # 创建标准格式
                standard_data = {
                    'graphs': graphs,
                    'props': props
                }
                
            elif 'structure' in data and 'e_form' in data:
                # 可能是原始MatBench格式
                structures = data['structure']
                e_forms = data['e_form']

                print(f"检测到原始MatBench格式")
                print(f"结构数量: {len(structures)}")
                print(f"属性数量: {len(e_forms)}")

                # 转换为标准格式
                graphs_dict = {}
                if isinstance(structures, dict):
                    for i, (key, structure) in enumerate(structures.items()):
                        graphs_dict[i] = structure
                elif isinstance(structures, list):
                    for i, structure in enumerate(structures):
                        graphs_dict[i] = structure

                props_dict = {"e_form": list(e_forms.values()) if isinstance(e_forms, dict) else e_forms}

                standard_data = {
                    'graphs': graphs_dict,
                    'props': props_dict
                }

            else:
                # 检查是否props是DataFrame格式
                graphs = data['graphs']
                props = data['props']

                print(f"检测到graphs+props格式，props类型: {type(props)}")

                # 如果props是DataFrame，转换为字典
                if hasattr(props, 'to_dict'):
                    print("转换pandas DataFrame为字典格式")
                    # 假设DataFrame只有一列是e_form
                    if hasattr(props, 'columns'):
                        print(f"DataFrame列: {list(props.columns)}")
                        if len(props.columns) == 1:
                            column_name = props.columns[0]
                            props_dict = {"e_form": props[column_name].tolist()}
                        elif 'e_form' in props.columns:
                            props_dict = {"e_form": props['e_form'].tolist()}
                        else:
                            # 使用第一列
                            column_name = props.columns[0]
                            props_dict = {"e_form": props[column_name].tolist()}
                    else:
                        props_dict = {"e_form": props.tolist()}
                elif isinstance(props, list):
                    props_dict = {"e_form": props}
                else:
                    print(f"无法处理的props类型: {type(props)}")
                    return False

                standard_data = {
                    'graphs': graphs,
                    'props': props_dict
                }
        
        else:
            print(f"不支持的数据类型: {type(data)}")
            return False
        
        # 验证转换结果
        graphs = standard_data['graphs']
        props = standard_data['props']
        
        if len(graphs) != len(props['e_form']):
            print(f"数据不一致: graphs={len(graphs)}, props={len(props['e_form'])}")
            return False
        
        # 保存转换后的数据
        with open(output_file, 'wb') as f:
            pickle.dump(standard_data, f)
        
        print(f"✓ 转换完成!")
        print(f"输出文件: {output_file}")
        print(f"样本数量: {len(graphs)}")
        
        # 显示统计信息
        e_form_values = props['e_form']
        print(f"形成能范围: {min(e_form_values):.3f} ~ {max(e_form_values):.3f} eV/atom")
        print(f"平均形成能: {sum(e_form_values)/len(e_form_values):.3f} eV/atom")
        
        return True
        
    except Exception as e:
        print(f"转换失败: {e}")
        return False

def main():
    """主函数"""
    print("检查现有MatBench数据文件")
    print("=" * 60)
    
    # 首先检查文件结构
    inspect_processed_data()
    
    print("\n" + "=" * 60)
    
    # 询问是否转换格式
    choice = input("是否将数据转换为标准格式? (y/n): ").lower().strip()
    
    if choice == 'y':
        if convert_to_standard_format():
            print("\n转换成功! 现在可以使用完整数据集进行训练了。")
            print("运行以下命令开始训练:")
            print("python property_prediction/train.py -c property_prediction/configs/comformer/comformer_matbench_mp_e_form_full.yaml")
        else:
            print("\n转换失败，请检查数据格式。")
    else:
        print("跳过转换。")

if __name__ == "__main__":
    main()
