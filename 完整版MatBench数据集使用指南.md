# 完整版MatBench数据集使用指南

## 📋 概述

MatBench是材料科学机器学习的标准基准数据集，包含多个材料性质预测任务。本指南将详细介绍如何获取和使用完整版的MatBench数据集进行Comformer模型训练。

## 🎯 支持的任务

目前支持的MatBench任务：
- **matbench_mp_e_form**: 材料形成能预测（约132,752个样本）
- **matbench_mp_gap**: 带隙预测
- **matbench_mp_is_metal**: 金属性预测
- **matbench_perovskites**: 钙钛矿稳定性预测
- **matbench_phonons**: 声子性质预测

## 📦 环境准备

### 1. 安装依赖包

```bash
# 激活conda环境
conda activate ppmat

# 安装MatBench库
pip install matbench

# 安装其他必要依赖
pip install pymatgen
pip install scikit-learn
```

### 2. 验证安装

```python
import matbench
from matbench.bench import MatbenchBenchmark
print("MatBench安装成功!")
```

## 📥 获取完整数据集

### 方法一：使用MatBench官方API（推荐）

创建数据下载脚本：

```python
# download_matbench_full.py
import os
import pickle
from matbench.bench import MatbenchBenchmark

def download_matbench_data():
    """下载完整的MatBench数据集"""
    
    # 创建数据目录
    os.makedirs("./data/matbench", exist_ok=True)
    
    # 初始化MatBench基准测试
    mb = MatbenchBenchmark(autoload=False)
    
    # 下载matbench_mp_e_form任务数据
    task_name = "matbench_mp_e_form"
    print(f"正在下载 {task_name} 数据集...")
    
    # 加载任务数据
    task = mb.matbench_mp_e_form
    task.load()
    
    # 获取完整数据
    df = task.df
    print(f"数据集大小: {len(df)} 个样本")
    
    # 转换为我们需要的格式
    graphs_dict = {}
    props_dict = {"e_form": []}
    
    for i, (idx, row) in enumerate(df.iterrows()):
        # 存储结构信息（使用pymatgen Structure对象）
        graphs_dict[i] = row['structure']
        # 存储属性值
        props_dict["e_form"].append(row['e_form'])
        
        if (i + 1) % 1000 == 0:
            print(f"已处理 {i + 1} 个样本...")
    
    # 保存为我们的格式
    data = {
        'graphs': graphs_dict,
        'props': props_dict
    }
    
    output_file = "./data/matbench/matbench_mp_e_form.pkl"
    with open(output_file, 'wb') as f:
        pickle.dump(data, f)
    
    print(f"完整数据集已保存到: {output_file}")
    print(f"总样本数: {len(graphs_dict)}")
    
    return output_file

if __name__ == "__main__":
    download_matbench_data()
```

### 方法二：从预处理数据扩展

如果已有部分数据，可以从现有数据扩展：

```python
# expand_matbench_data.py
import pickle
import os
from matbench.bench import MatbenchBenchmark

def expand_from_existing():
    """从现有数据扩展到完整数据集"""
    
    # 检查现有数据
    existing_file = "./data/matbench/matbench_mp_e_form_processed.pkl"
    if not os.path.exists(existing_file):
        print("现有数据文件不存在，请先运行方法一")
        return
    
    # 下载完整数据
    mb = MatbenchBenchmark(autoload=False)
    task = mb.matbench_mp_e_form
    task.load()
    df = task.df
    
    # 转换格式
    graphs_dict = {}
    props_dict = {"e_form": []}
    
    for i, (idx, row) in enumerate(df.iterrows()):
        graphs_dict[i] = row['structure']
        props_dict["e_form"].append(row['e_form'])
    
    # 保存完整数据
    data = {'graphs': graphs_dict, 'props': props_dict}
    output_file = "./data/matbench/matbench_mp_e_form_full.pkl"
    
    with open(output_file, 'wb') as f:
        pickle.dump(data, f)
    
    print(f"完整数据集已保存: {output_file}")
    print(f"样本数量: {len(graphs_dict)}")

if __name__ == "__main__":
    expand_from_existing()
```

## ⚙️ 配置文件设置

### 创建完整数据集配置文件

```yaml
# property_prediction/configs/comformer/comformer_matbench_mp_e_form_full.yaml
Global:
  label_names:
  - e_form
  do_train: true
  do_eval: true
  do_test: true
  split_dataset_ratio:
    train: 0.8
    val: 0.1
    test: 0.1
  graph_converter:
    __class_name__: ComformerGraphConverter
    __init_params__:
      cutoff: 5.0
      num_cpus: 8  # 增加CPU核心数以加速处理
      atom_features: cgcnn
      max_neighbors: 25

Trainer:
  max_epochs: 200  # 增加训练轮数
  seed: 42
  output_dir: ./output/comformer_matbench_mp_e_form_full
  save_freq: 50
  log_freq: 100
  start_eval_epoch: 10
  eval_freq: 20
  pretrained_model_path: null
  resume_from_checkpoint: null
  use_amp: true  # 启用混合精度训练
  amp_level: O1
  eval_with_no_grad: true
  gradient_accumulation_steps: 2  # 梯度累积
  best_metric_indicator: eval_metric
  name_for_best_metric: e_form
  greater_is_better: false
  compute_metric_during_train: true
  metric_strategy_during_eval: epoch
  use_visualdl: false
  use_wandb: false
  use_tensorboard: false

Model:
  __class_name__: iComformer
  __init_params__:
    conv_layers: 4
    edge_layers: 1
    atom_input_features: 92
    edge_features: 256
    triplet_input_features: 256
    node_features: 256
    fc_features: 256
    output_features: 1
    node_layer_head: 1
    edge_layer_head: 1
    property_name: ${Global.label_names}

Metric:
  e_form:
    __class_name__: paddle.nn.L1Loss
    __init_params__: {}

Optimizer:
  __class_name__: AdamW
  __init_params__:
    lr:
      __class_name__: OneCycleLR
      __init_params__:
        max_learning_rate: 0.001

Dataset:
  train:
    dataset:
      __class_name__: MatBenchDataset
      __init_params__:
        path: ./data/matbench
        task_name: matbench_mp_e_form_full  # 使用完整数据集
        property_name: e_form
        overwrite: false
        # max_samples: null  # 不限制样本数量，使用全部数据
        transforms:
        - __class_name__: ComformerCompatTransform
          __init_params__: {}
    num_workers: 4  # 增加数据加载线程
    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: true
        drop_last: true
        batch_size: 64  # 增加批次大小
  val:
    dataset:
      __class_name__: MatBenchDataset
      __init_params__:
        path: ./data/matbench
        task_name: matbench_mp_e_form_full
        property_name: e_form
        overwrite: false
        transforms:
        - __class_name__: ComformerCompatTransform
          __init_params__: {}
    num_workers: 4
    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: false
        drop_last: false
        batch_size: 64
  test:
    dataset:
      __class_name__: MatBenchDataset
      __init_params__:
        path: ./data/matbench
        task_name: matbench_mp_e_form_full
        property_name: e_form
        overwrite: false
        transforms:
        - __class_name__: ComformerCompatTransform
          __init_params__: {}
    num_workers: 4
    sampler:
      __class_name__: BatchSampler
      __init_params__:
        shuffle: false
        drop_last: false
        batch_size: 64

Predict:
  graph_converter: ${Global.graph_converter}
  eval_with_no_grad: true
```

## 🚀 使用方法

### 1. 下载完整数据集

```bash
# 运行数据下载脚本
python download_matbench_full.py
```

### 2. 开始训练

```bash
# 使用完整数据集训练
python property_prediction/train.py -c property_prediction/configs/comformer/comformer_matbench_mp_e_form_full.yaml
```

### 3. 监控训练进度

```bash
# 查看训练日志
tail -f ./output/comformer_matbench_mp_e_form_full_*/run.log
```

## 📊 性能优化建议

### 1. 硬件配置建议

- **内存**: 至少32GB RAM（完整数据集较大）
- **GPU**: 建议使用V100或A100等高性能GPU
- **存储**: 至少100GB可用空间用于数据和缓存

### 2. 训练优化

```yaml
# 在配置文件中启用以下优化选项：
Trainer:
  use_amp: true          # 混合精度训练，节省显存
  gradient_accumulation_steps: 4  # 梯度累积，模拟更大批次
  
Dataset:
  train:
    num_workers: 8       # 增加数据加载线程
    sampler:
      batch_size: 128    # 根据GPU显存调整批次大小
```

### 3. 缓存优化

```python
# 在数据集配置中启用缓存
dataset:
  __init_params__:
    cache_path: "./data/matbench_cache"  # 指定缓存路径
    overwrite: false                     # 重用已有缓存
```

## 🔍 数据集信息

### MatBench MP E_form 完整数据集统计

- **总样本数**: ~132,752
- **训练集**: ~106,202 (80%)
- **验证集**: ~13,275 (10%)  
- **测试集**: ~13,275 (10%)
- **属性范围**: 形成能 (-10 到 +5 eV/atom)
- **结构类型**: 无机晶体材料

### 预期训练时间

- **小规模测试** (1000样本): ~30分钟
- **中等规模** (10000样本): ~5小时
- **完整数据集** (132k样本): ~2-3天

## ⚠️ 注意事项

1. **内存管理**: 完整数据集需要大量内存，建议分批处理
2. **缓存策略**: 首次运行会生成大量缓存文件，确保有足够存储空间
3. **训练时间**: 完整数据集训练时间较长，建议使用断点续训功能
4. **数据验证**: 训练前验证数据完整性和格式正确性

## 🎯 预期结果

使用完整MatBench数据集训练的Comformer模型预期性能：

- **MAE (Mean Absolute Error)**: < 0.1 eV/atom
- **RMSE (Root Mean Square Error)**: < 0.15 eV/atom
- **R² Score**: > 0.95

这些指标应该显著优于小规模数据集的训练结果。
