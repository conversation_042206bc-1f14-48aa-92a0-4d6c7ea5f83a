# Copyright (c) 2023 PaddlePaddle Authors. All Rights Reserved.

# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at

#     http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


from __future__ import annotations

import numbers
from collections.abc import Mapping
from collections.abc import Sequence
from typing import Any
from typing import List

import numpy as np
import paddle
import pgl

from ppmat.datasets.custom_data_type import ConcatData
from ppmat.datasets.custom_data_type import ConcatNumpyWarper


class DefaultCollator:
    """
    Robust collate function for batching data with enhanced error handling.
    """

    def __call__(self, batch: List[Any]) -> Any:
        """
        Collate a batch of data with robust error handling.
        """
        if not batch:
            return None
            
        sample = batch[0]
        
        # Handle None samples
        if sample is None:
            return None
            
        # Handle ConcatNumpyWarper
        elif isinstance(sample, ConcatNumpyWarper):
            batch = np.concatenate(batch, axis=0)
            return batch
            
        # Handle numpy arrays
        elif isinstance(sample, np.ndarray):
            try:
                batch = np.stack(batch, axis=0)
                return batch
            except Exception as e:
                print(f"Warning: Failed to stack numpy arrays: {e}")
                return batch
                
        # Handle paddle tensors
        elif isinstance(sample, (paddle.Tensor, paddle.framework.core.eager.Tensor)):
            try:
                return paddle.stack(batch, axis=0)
            except Exception as e:
                print(f"Warning: Failed to stack tensors: {e}")
                return batch
                
        # Handle numbers
        elif isinstance(sample, numbers.Number):
            batch = np.array(batch)
            return batch
            
        # Handle strings and bytes
        elif isinstance(sample, (str, bytes)):
            return batch
            
        # Handle dictionaries (Mapping) - ROBUST VERSION
        elif isinstance(sample, Mapping):
            return self._handle_mapping_batch(batch)
            
        # Handle sequences (lists, tuples)
        elif isinstance(sample, Sequence):
            return self._handle_sequence_batch(batch)
            
        # Handle PGL graphs
        elif str(type(sample)) == "<class 'pgl.graph.Graph'>":
            try:
                graphs = pgl.Graph.batch(batch)
                return graphs
            except Exception as e:
                print(f"Warning: Failed to batch PGL graphs: {e}")
                return batch
                
        # Handle ConcatData
        elif isinstance(sample, ConcatData):
            try:
                return ConcatData.batch(batch)
            except Exception as e:
                print(f"Warning: Failed to batch ConcatData: {e}")
                return batch
                
        # Unknown type
        else:
            raise TypeError(
                "batch data can only contains: paddle.Tensor, numpy.ndarray, "
                f"dict, list, number, None, pgl.Graph, but got {type(sample)}"
            )

    def _handle_mapping_batch(self, batch: List[Mapping]) -> Mapping:
        """
        Robust handling of dictionary batches.
        """
        try:
            # Get all keys from all samples
            all_keys = [set(d.keys()) for d in batch]
            
            # Check if all samples have the same keys
            if not all(keys == all_keys[0] for keys in all_keys):
                print(f"Warning: Inconsistent keys in batch samples:")
                for i, keys in enumerate(all_keys[:5]):  # Show first 5 samples
                    print(f"  Sample {i}: {sorted(keys)}")
                
                # Find common keys
                common_keys = set.intersection(*all_keys)
                print(f"  Using common keys: {sorted(common_keys)}")
                
                if not common_keys:
                    # If no common keys, try to use keys from first sample
                    print("  No common keys found, using keys from first sample")
                    common_keys = all_keys[0]
                
                # Process only common keys
                result = {}
                for key in common_keys:
                    try:
                        # Only include samples that have this key
                        key_batch = [d[key] for d in batch if key in d]
                        if key_batch:  # Only process if we have data
                            result[key] = self(key_batch)
                    except Exception as e:
                        print(f"  Error processing key '{key}': {e}")
                        continue
                        
                return result
            
            # Normal case: all samples have the same keys
            result = {}
            sample_keys = list(batch[0].keys())
            
            for key in sample_keys:
                try:
                    result[key] = self([d[key] for d in batch])
                except Exception as e:
                    print(f"  Error processing key '{key}': {e}")
                    # Try safe processing
                    try:
                        key_batch = [d[key] for d in batch if key in d]
                        if key_batch:
                            result[key] = self(key_batch)
                    except Exception as e2:
                        print(f"  Failed to process key '{key}' safely: {e2}")
                        continue
                        
            return result
            
        except Exception as e:
            print(f"Error in mapping batch processing: {e}")
            print(f"Batch info: {len(batch)} samples")
            print(f"Sample types: {[type(d) for d in batch[:3]]}")
            # Return original batch as fallback
            return batch

    def _handle_sequence_batch(self, batch: List[Sequence]) -> List:
        """
        Robust handling of sequence batches.
        """
        try:
            sample_fields_num = len(batch[0])
            
            # Check if all sequences have the same length
            field_lengths = [len(sample) for sample in batch]
            if not all(length == sample_fields_num for length in field_lengths):
                print(f"Warning: Inconsistent sequence lengths in batch:")
                print(f"  Expected: {sample_fields_num}")
                print(f"  Got: {field_lengths[:10]}...")  # Show first 10
                
                # Try to handle by truncating to minimum length
                min_length = min(field_lengths)
                print(f"  Truncating all sequences to length: {min_length}")
                
                truncated_batch = [sample[:min_length] for sample in batch]
                return [self(fields) for fields in zip(*truncated_batch)]
            
            # Normal case: all sequences have the same length
            return [self(fields) for fields in zip(*batch)]
            
        except Exception as e:
            print(f"Error in sequence batch processing: {e}")
            print(f"Batch info: {len(batch)} samples")
            print(f"Sample lengths: {[len(s) for s in batch[:5]]}")
            # Return original batch as fallback
            return batch
